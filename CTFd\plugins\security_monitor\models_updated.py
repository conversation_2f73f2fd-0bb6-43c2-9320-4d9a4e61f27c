import json
from datetime import datetime, timedelta
from CTFd.models import db
from sqlalchemy.exc import OperationalError, ProgrammingError

def create_all():
    """Create all database tables for the security monitor plugin"""
    try:
        SecurityEvent.__table__.create(db.engine, checkfirst=True)
        SecurityConfig.__table__.create(db.engine, checkfirst=True)
        SecurityAlert.__table__.create(db.engine, checkfirst=True)
        SecurityBan.__table__.create(db.engine, checkfirst=True)
        SecurityMetrics.__table__.create(db.engine, checkfirst=True)
        PlatformAnalytics.__table__.create(db.engine, checkfirst=True)
        ContainerMetrics.__table__.create(db.engine, checkfirst=True)
        PerformanceMetrics.__table__.create(db.engine, checkfirst=True)
        return True
    except (OperationalError, ProgrammingError) as e:
        print(f"Error creating security_monitor tables: {str(e)}")
        return False

class SecurityEvent(db.Model):
    """Model for storing security events"""
    __tablename__ = "security_events"

    id = db.Column(db.Integer, primary_key=True)
    event_type = db.Column(db.String(50), nullable=False)  # rate_limit, ddos, suspicious_activity, etc.
    severity = db.Column(db.String(20), default='medium')  # low, medium, high, critical
    source_ip = db.Column(db.String(45), nullable=False)  # IPv4 or IPv6
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    endpoint = db.Column(db.String(255), nullable=True)
    method = db.Column(db.String(10), nullable=True)
    user_agent = db.Column(db.Text, nullable=True)
    details = db.Column(db.JSON, nullable=True)  # Additional event details
    timestamp = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    processed = db.Column(db.Boolean, default=False)
    threat_score = db.Column(db.Float, default=0.0, nullable=True)  # Added threat score field

    def __init__(self, **kwargs):
        # Set default threat_score if not provided
        if 'threat_score' not in kwargs:
            kwargs['threat_score'] = 0.0
        super(SecurityEvent, self).__init__(**kwargs)

    def to_dict(self):
        return {
            'id': self.id,
            'event_type': self.event_type,
            'severity': self.severity,
            'source_ip': self.source_ip,
            'user_id': self.user_id,
            'endpoint': self.endpoint,
            'method': self.method,
            'user_agent': self.user_agent,
            'details': self.details,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'processed': self.processed,
            'threat_score': self.threat_score or 0.0
        }

class SecurityConfig(db.Model):
    """Model for storing security configuration"""
    __tablename__ = "security_config"

    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(255), unique=True, nullable=False)
    value = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    @classmethod
    def get_config(cls, key, default=None):
        """Get a configuration value"""
        config = cls.query.filter_by(key=key).first()
        if config:
            try:
                return json.loads(config.value)
            except (json.JSONDecodeError, TypeError):
                return config.value
        return default

    @classmethod
    def set_config(cls, key, value):
        """Set a configuration value"""
        config = cls.query.filter_by(key=key).first()
        if config:
            config.value = json.dumps(value) if not isinstance(value, str) else value
            config.updated_at = datetime.utcnow()
        else:
            config = cls(
                key=key,
                value=json.dumps(value) if not isinstance(value, str) else value
            )
            db.session.add(config)
        db.session.commit()
        return config

    @classmethod
    def get_all_configs(cls):
        """Get all configuration values"""
        configs = cls.query.all()
        result = {}
        for config in configs:
            try:
                result[config.key] = json.loads(config.value)
            except (json.JSONDecodeError, TypeError):
                result[config.key] = config.value
        return result

class SecurityAlert(db.Model):
    """Model for storing security alerts"""
    __tablename__ = "security_alerts"

    id = db.Column(db.Integer, primary_key=True)
    alert_type = db.Column(db.String(50), nullable=False)
    severity = db.Column(db.String(20), default='medium')
    title = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text, nullable=True)
    source_ip = db.Column(db.String(45), nullable=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    event_count = db.Column(db.Integer, default=1)
    status = db.Column(db.String(20), default='active')  # active, acknowledged, resolved
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    resolved_at = db.Column(db.DateTime, nullable=True)
    resolved_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)

    def to_dict(self):
        return {
            'id': self.id,
            'alert_type': self.alert_type,
            'severity': self.severity,
            'title': self.title,
            'description': self.description,
            'source_ip': self.source_ip,
            'user_id': self.user_id,
            'event_count': self.event_count,
            'status': self.status,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'resolved_at': self.resolved_at.isoformat() if self.resolved_at else None,
            'resolved_by': self.resolved_by
        }

class SecurityBan(db.Model):
    """Model for storing security-based IP bans"""
    __tablename__ = "security_bans"

    id = db.Column(db.Integer, primary_key=True)
    ip_address = db.Column(db.String(45), nullable=False, unique=True)
    reason = db.Column(db.String(255), nullable=False)
    ban_type = db.Column(db.String(20), default='temporary')  # temporary, permanent
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    expires_at = db.Column(db.DateTime, nullable=True)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    is_active = db.Column(db.Boolean, default=True)

    @classmethod
    def is_banned(cls, ip_address):
        """Check if an IP address is banned"""
        try:
            ban = cls.query.filter_by(
                ip_address=ip_address,
                is_active=True
            ).first()

            if ban:
                # Check if temporary ban has expired
                if ban.ban_type == 'temporary' and ban.expires_at:
                    if datetime.utcnow() > ban.expires_at:
                        ban.is_active = False
                        db.session.commit()
                        return False
                return True
            return False
        except Exception as e:
            # If database error occurs, assume not banned to avoid blocking legitimate users
            from flask import current_app
            if current_app:
                current_app.logger.error(f"Failed to check ban status for IP {ip_address}: {str(e)}")
            return False

    def to_dict(self):
        return {
            'id': self.id,
            'ip_address': self.ip_address,
            'reason': self.reason,
            'ban_type': self.ban_type,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'created_by': self.created_by,
            'is_active': self.is_active
        }

class SecurityMetrics(db.Model):
    """Model for storing security metrics and statistics"""
    __tablename__ = "security_metrics"

    id = db.Column(db.Integer, primary_key=True)
    metric_type = db.Column(db.String(50), nullable=False)  # requests_per_minute, failed_logins, etc.
    metric_value = db.Column(db.Float, nullable=False)
    source_ip = db.Column(db.String(45), nullable=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    metric_metadata = db.Column(db.JSON, nullable=True)

    @classmethod
    def record_metric(cls, metric_type, value, source_ip=None, user_id=None, metadata=None):
        """Record a security metric"""
        try:
            metric = cls(
                metric_type=metric_type,
                metric_value=value,
                source_ip=source_ip,
                user_id=user_id,
                metric_metadata=metadata
            )
            db.session.add(metric)
            db.session.commit()
            return metric
        except Exception as e:
            db.session.rollback()
            # Log error but don't raise to prevent breaking the application
            from flask import current_app
            if current_app:
                current_app.logger.error(f"Failed to record security metric: {str(e)}")
            return None

    @classmethod
    def get_metrics(cls, metric_type, hours=24):
        """Get metrics for a specific type within the last N hours"""
        from_time = datetime.utcnow() - timedelta(hours=hours)
        return cls.query.filter(
            cls.metric_type == metric_type,
            cls.timestamp >= from_time
        ).all()

    def to_dict(self):
        return {
            'id': self.id,
            'metric_type': self.metric_type,
            'metric_value': self.metric_value,
            'source_ip': self.source_ip,
            'user_id': self.user_id,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'metadata': self.metric_metadata
        }


class PlatformAnalytics(db.Model):
    """Model for storing platform analytics and usage metrics"""
    __tablename__ = "platform_analytics"

    id = db.Column(db.Integer, primary_key=True)
    metric_category = db.Column(db.String(50), nullable=False)  # user_activity, challenge_engagement, etc.
    metric_name = db.Column(db.String(100), nullable=False)
    metric_value = db.Column(db.Float, nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    team_id = db.Column(db.Integer, db.ForeignKey('teams.id'), nullable=True)
    challenge_id = db.Column(db.Integer, db.ForeignKey('challenges.id'), nullable=True)
    session_id = db.Column(db.String(128), nullable=True)
    event_metadata = db.Column(db.JSON, nullable=True)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)

    def to_dict(self):
        return {
            'id': self.id,
            'metric_category': self.metric_category,
            'metric_name': self.metric_name,
            'metric_value': self.metric_value,
            'user_id': self.user_id,
            'team_id': self.team_id,
            'challenge_id': self.challenge_id,
            'session_id': self.session_id,
            'metadata': self.event_metadata,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None
        }


class ContainerMetrics(db.Model):
    """Model for storing container lifecycle and performance metrics"""
    __tablename__ = "container_metrics"

    id = db.Column(db.Integer, primary_key=True)
    container_type = db.Column(db.String(20), nullable=False)  # whale, desktop
    container_id = db.Column(db.String(256), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    challenge_id = db.Column(db.Integer, db.ForeignKey('challenges.id'), nullable=True)
    action = db.Column(db.String(50), nullable=False)  # created, started, stopped, destroyed, renewed
    resource_usage = db.Column(db.JSON, nullable=True)  # CPU, memory, network stats
    duration = db.Column(db.Float, nullable=True)  # Duration in seconds
    status = db.Column(db.String(20), nullable=True)  # success, failed, timeout
    error_message = db.Column(db.Text, nullable=True)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)

    def to_dict(self):
        return {
            'id': self.id,
            'container_type': self.container_type,
            'container_id': self.container_id,
            'user_id': self.user_id,
            'challenge_id': self.challenge_id,
            'action': self.action,
            'resource_usage': self.resource_usage,
            'duration': self.duration,
            'status': self.status,
            'error_message': self.error_message,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None
        }


class PerformanceMetrics(db.Model):
    """Model for storing system performance metrics"""
    __tablename__ = "performance_metrics"

    id = db.Column(db.Integer, primary_key=True)
    component = db.Column(db.String(50), nullable=False)  # database, redis, nginx, ctfd, docker
    metric_type = db.Column(db.String(50), nullable=False)  # response_time, cpu_usage, memory_usage, etc.
    metric_value = db.Column(db.Float, nullable=False)
    unit = db.Column(db.String(20), nullable=True)  # ms, %, MB, etc.
    threshold_warning = db.Column(db.Float, nullable=True)
    threshold_critical = db.Column(db.Float, nullable=True)
    perf_metadata = db.Column(db.JSON, nullable=True)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)

    def to_dict(self):
        return {
            'id': self.id,
            'component': self.component,
            'metric_type': self.metric_type,
            'metric_value': self.metric_value,
            'unit': self.unit,
            'threshold_warning': self.threshold_warning,
            'threshold_critical': self.threshold_critical,
            'metadata': self.perf_metadata,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None
        }

    @classmethod
    def record_metric(cls, metric_type, value, source_ip=None, user_id=None, metadata=None):
        """Record a performance metric"""
        try:
            metric = cls(
                metric_type=metric_type,
                metric_value=value,
                perf_metadata=metadata
            )
            db.session.add(metric)
            db.session.commit()
            return metric
        except Exception as e:
            db.session.rollback()
            # Log error but don't raise to prevent breaking the application
            from flask import current_app
            if current_app:
                current_app.logger.error(f"Failed to record performance metric: {str(e)}")
            return None

    @classmethod
    def get_metrics(cls, metric_type, hours=24):
        """Get metrics for a specific type within the last N hours"""
        from_time = datetime.utcnow() - timedelta(hours=hours)
        return cls.query.filter(
            cls.metric_type == metric_type,
            cls.timestamp >= from_time
        ).all()
