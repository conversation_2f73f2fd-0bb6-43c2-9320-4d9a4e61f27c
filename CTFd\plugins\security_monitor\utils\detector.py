import time
import re
import json
import os
from datetime import datetime, timedelta
from collections import defaultdict
from flask import request, current_app
from CTFd.cache import cache
from CTFd.models import db
from ..models import SecurityEvent, SecurityConfig, SecurityAlert, SecurityBan, SecurityMetrics

class SecurityDetector:
    """Security threat detection engine"""

    def __init__(self):
        self.request_counts = defaultdict(list)
        self.suspicious_patterns = [
            r'(?i)(union|select|insert|delete|drop|create|alter|exec|script)',  # SQL injection
            r'(?i)(<script|javascript:|vbscript:|onload=|onerror=)',  # XSS
            r'(?i)(\.\.\/|\.\.\\|\/etc\/|\/proc\/|\/sys\/)',  # Path traversal
            r'(?i)(cmd=|exec=|system=|shell=)',  # Command injection
            r'(?i)(eval\(|base64_decode|gzinflate)',  # Code injection
        ]
        self.bot_patterns = [
            r'(?i)(bot|crawler|spider|scraper|scanner)',
            r'(?i)(nmap|sqlmap|nikto|dirb|gobuster)',
            r'(?i)(python-requests|curl|wget|httpclient)',
        ]

    def check_rate_limit(self, ip, endpoint):
        """Check if IP has exceeded rate limits - Enhanced with logging"""
        if not SecurityConfig.get_config('security_rate_limit_enabled', True):
            return False

        # Enhanced endpoint-specific rate limits for better security
        endpoint_limits = {
            'auth.login': {'limit': 3, 'window': 5},  # 3 attempts per 5 seconds (more restrictive)
            'auth.register': {'limit': 1, 'window': 60},  # 1 registration per minute
            'api.challenges_challenge_attempt': {'limit': 10, 'window': 5},  # 10 flag attempts per 5 seconds
            'admin': {'limit': 5, 'window': 1},  # 5 admin requests per second
            'api': {'limit': 20, 'window': 5},  # 20 API calls per 5 seconds
            'static': {'limit': 100, 'window': 5},  # 100 static requests per 5 seconds
        }

        # Default values
        limit = SecurityConfig.get_config('security_rate_limit_requests', 100)
        window = SecurityConfig.get_config('security_rate_limit_window', 60)
        endpoint_key = 'general'

        # Check if endpoint matches specific patterns
        if endpoint:
            if endpoint.startswith('admin.'):
                endpoint_key = 'admin'
            elif endpoint in endpoint_limits:
                endpoint_key = endpoint
            elif 'attempt' in endpoint:
                endpoint_key = 'api.challenges_challenge_attempt'

        # Get specific limits if available
        if endpoint_key in endpoint_limits:
            limit = endpoint_limits[endpoint_key]['limit']
            window = endpoint_limits[endpoint_key]['window']

        # Use cache for rate limiting
        cache_key = f"rate_limit:{ip}:{endpoint_key}"
        current_count = cache.get(cache_key) or 0

        if current_count >= limit:
            # ALWAYS log rate limit events for real-time monitoring
            try:
                from flask import has_request_context
                if has_request_context():
                    from flask import session
                    user_id = session.get('id')
                    
                    # Create and log security event immediately
                    event = SecurityEvent(
                        event_type='rate_limit_exceeded',
                        severity='high',
                        source_ip=ip,
                        user_id=user_id,
                        endpoint=endpoint,
                        method=request.method if has_request_context() else None,
                        user_agent=request.headers.get('User-Agent') if has_request_context() else None,
                        details={'endpoint': endpoint, 'limit': limit, 'window': window, 'count': current_count},
                        timestamp=datetime.utcnow()
                    )
                    
                    # Log to database
                    db.session.add(event)
                    db.session.commit()
                    
                    # Log to file immediately
                    self._log_to_json_file(event)
                    
            except Exception as e:
                if current_app:
                    current_app.logger.error(f"Failed to log rate limit event: {str(e)}")
            
            # Record metric
            try:
                SecurityMetrics.record_metric(
                    'rate_limit_exceeded',
                    current_count,
                    source_ip=ip,
                    metadata={'endpoint': endpoint, 'limit': limit, 'window': window}
                )
            except Exception:
                pass
                
            return True

        # Increment counter
        cache.set(cache_key, current_count + 1, timeout=window)
        return False

    def detect_ddos(self, ip):
        """Detect potential DDoS attacks - Enhanced with real-time logging"""
        if not SecurityConfig.get_config('security_ddos_detection_enabled', True):
            return False

        # Enhanced DDoS detection with lower, more realistic thresholds
        threshold = SecurityConfig.get_config('security_ddos_threshold', 300)  # 300 req/min instead of 1000
        window = SecurityConfig.get_config('security_ddos_window', 60)

        # Check requests per minute for this IP
        cache_key = f"ddos_check:{ip}"
        request_count = cache.get(cache_key) or 0

        # Increment counter first
        cache.set(cache_key, request_count + 1, timeout=window)

        if request_count >= threshold:
            # Record DDoS event immediately for real-time monitoring
            ddos_alert_key = f"ddos_alert:{ip}"
            if not cache.get(ddos_alert_key):
                try:
                    # Log security event immediately
                    from flask import has_request_context, session
                    user_id = session.get('id') if has_request_context() else None
                    
                    event = SecurityEvent(
                        event_type='ddos_detected',
                        severity='critical',
                        source_ip=ip,
                        user_id=user_id,
                        endpoint=request.endpoint if has_request_context() else None,
                        method=request.method if has_request_context() else None,
                        user_agent=request.headers.get('User-Agent') if has_request_context() else None,
                        details={'threshold': threshold, 'window': window, 'request_count': request_count},
                        timestamp=datetime.utcnow()
                    )
                    
                    # Log to database
                    db.session.add(event)
                    db.session.commit()
                    
                    # Log to file immediately
                    self._log_to_json_file(event)
                    
                    # Record metric
                    SecurityMetrics.record_metric(
                        'ddos_detected',
                        request_count,
                        source_ip=ip,
                        metadata={'threshold': threshold, 'window': window}
                    )

                    # Create alert
                    self._create_alert(
                        'ddos_attack',
                        'critical',
                        f'DDoS attack detected from {ip}',
                        f'IP {ip} made {request_count} requests in {window} seconds',
                        ip
                    )

                    # Set alert flag to prevent spam
                    cache.set(ddos_alert_key, True, timeout=window)
                    
                except Exception as e:
                    if current_app:
                        current_app.logger.error(f"Failed to process DDoS detection: {str(e)}")
                        
            return True

        return False

    def detect_suspicious_activity(self, ip, endpoint, request_obj):
        """Detect suspicious activity patterns"""
        if not SecurityConfig.get_config('security_suspicious_activity_enabled', True):
            return False

        suspicious_score = 0
        details = {}

        # Check for suspicious patterns in URL and parameters
        full_url = str(request_obj.url)
        query_string = str(request_obj.query_string)

        for pattern in self.suspicious_patterns:
            if re.search(pattern, full_url) or re.search(pattern, query_string):
                suspicious_score += 10
                details['suspicious_pattern'] = pattern
                break

        # Check for bot/scanner user agents
        user_agent = request_obj.headers.get('User-Agent', '')
        for pattern in self.bot_patterns:
            if re.search(pattern, user_agent):
                suspicious_score += 5
                details['bot_pattern'] = pattern
                break

        # Check for unusual request methods
        if request_obj.method in ['TRACE', 'CONNECT', 'OPTIONS']:
            suspicious_score += 3
            details['unusual_method'] = request_obj.method

        # Check for missing or suspicious headers
        if not request_obj.headers.get('User-Agent'):
            suspicious_score += 2
            details['missing_user_agent'] = True

        if not request_obj.headers.get('Accept'):
            suspicious_score += 1
            details['missing_accept'] = True

        # Check for rapid endpoint scanning
        cache_key = f"endpoint_scan:{ip}"
        endpoints = cache.get(cache_key) or []
        if endpoint not in endpoints:
            endpoints.append(endpoint)
            # Keep only last 50 endpoints to prevent memory issues
            if len(endpoints) > 50:
                endpoints = endpoints[-50:]
            cache.set(cache_key, endpoints, timeout=300)  # 5 minutes

        if len(endpoints) > 20:  # Accessing many different endpoints quickly
            suspicious_score += 8
            details['endpoint_scanning'] = len(endpoints)

        # Record suspicious activity metric
        if suspicious_score > 0:
            try:
                SecurityMetrics.record_metric(
                    'suspicious_activity',
                    suspicious_score,
                    source_ip=ip,
                    metadata=details
                )
            except Exception as e:
                if current_app:
                    current_app.logger.error(f"Failed to record suspicious activity metric: {str(e)}")

        # Enhanced threshold with geographic and behavioral factors
        return suspicious_score >= 8  # Lower threshold for better detection

    def _check_flag_brute_force(self, source_ip, user_id, challenge_id):
        """Check for flag submission brute force attempts"""
        try:
            # Check recent failed attempts from this IP
            recent_failures = SecurityEvent.query.filter(
                SecurityEvent.source_ip == source_ip,
                SecurityEvent.event_type.in_(['flag_submission_failure', 'flag_submission_ratelimited']),
                SecurityEvent.timestamp >= datetime.utcnow() - timedelta(minutes=5)
            ).count()

            # Check recent failed attempts for specific challenge
            if challenge_id:
                try:
                    # Safe way to query JSON field
                    challenge_failures = SecurityEvent.query.filter(
                        SecurityEvent.source_ip == source_ip,
                        SecurityEvent.event_type == 'flag_submission_failure',
                        SecurityEvent.details['challenge_id'].astext == str(challenge_id),
                        SecurityEvent.timestamp >= datetime.utcnow() - timedelta(minutes=10)
                    ).count()
                except Exception:
                    # Fallback if JSON query fails
                    challenge_failures = 0
            else:
                challenge_failures = 0

            # Trigger alerts based on thresholds
            if recent_failures >= 20:  # 20 failures in 5 minutes
                self._create_alert(
                    'flag_brute_force_critical',
                    'critical',
                    f'Critical flag brute force from {source_ip}',
                    f'IP {source_ip} made {recent_failures} failed flag attempts in 5 minutes',
                    source_ip,
                    user_id
                )
            elif recent_failures >= 10:  # 10 failures in 5 minutes
                self._create_alert(
                    'flag_brute_force_high',
                    'high',
                    f'High flag brute force from {source_ip}',
                    f'IP {source_ip} made {recent_failures} failed flag attempts in 5 minutes',
                    source_ip,
                    user_id
                )
            elif challenge_failures >= 15:  # 15 failures on same challenge in 10 minutes
                self._create_alert(
                    'challenge_brute_force',
                    'high',
                    f'Challenge brute force from {source_ip}',
                    f'IP {source_ip} made {challenge_failures} failed attempts on challenge {challenge_id}',
                    source_ip,
                    user_id
                )

        except Exception as e:
            if current_app:
                current_app.logger.error(f"Failed to check flag brute force: {str(e)}")

    def log_security_event(self, event_type, severity, source_ip, user_id=None, details=None):
        """Log a security event to both security system and CTFd native logs"""
        try:
            # Safely get request context information
            endpoint = None
            method = None
            user_agent = None

            try:
                from flask import has_request_context
                if has_request_context():
                    endpoint = request.endpoint
                    method = request.method
                    user_agent = request.headers.get('User-Agent')
            except:
                pass  # No request context available

            event = SecurityEvent(
                event_type=event_type,
                severity=severity,
                source_ip=source_ip,
                user_id=user_id,
                endpoint=endpoint,
                method=method,
                user_agent=user_agent,
                details=details or {},
                timestamp=datetime.utcnow()
            )

            db.session.add(event)
            db.session.commit()

            # Also log to JSON file as per document requirement
            self._log_to_json_file(event)

            # Log to CTFd's native logging system for flag attempts
            self._log_to_ctfd_native(event_type, severity, source_ip, user_id, details)

            # Check if we should create an alert
            self._check_alert_conditions(event)

            # Check if we should auto-ban
            self._check_auto_ban(source_ip, event_type)

            return event
        except Exception as e:
            # Rollback on error
            db.session.rollback()
            if current_app:
                current_app.logger.error(f"Failed to log security event: {str(e)}")
            return None

    def _log_to_json_file(self, event):
        """Log security event to JSON file"""
        try:
            log_dir = '/var/log/CTFd'
            log_file = os.path.join(log_dir, 'security.log')

            # Create directory if it doesn't exist
            os.makedirs(log_dir, exist_ok=True)

            # Prepare log entry with consistent format
            log_entry = {
                'timestamp': event.timestamp.isoformat(),
                'event_type': event.event_type,
                'severity': event.severity,
                'ip': event.source_ip,
                'user_id': event.user_id,
                'endpoint': event.endpoint,
                'method': event.method,
                'user_agent': event.user_agent[:200] if event.user_agent else None,
                'details': event.details or {}
            }

            # Write to file with explicit flush
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(log_entry) + '\n')
                f.flush()
                
        except Exception as e:
            # Don't let logging errors break the application
            if current_app:
                current_app.logger.error(f"Failed to write security log: {str(e)}")
            # Fallback: write to stdout for container logs
            try:
                print(f"SECURITY_EVENT: {event.event_type} from {event.source_ip}")
            except:
                pass

    def _log_to_ctfd_native(self, event_type, severity, source_ip, user_id, details):
        """Log security events to CTFd's native logging system"""
        try:
            from CTFd.utils.logging import log
            from flask import session

            # Only log flag-related events to submissions logger
            if event_type.startswith('flag_submission'):
                # Format details for logging
                challenge_id = details.get('challenge_id', 'unknown') if details else 'unknown'
                status_code = details.get('status_code', 'unknown') if details else 'unknown'
                submission_length = details.get('submission_length', 0) if details else 0

                # Determine log message based on event type
                if event_type == 'flag_submission_success':
                    log_msg = "[{date}] SECURITY: {name} (IP: {ip}) successful flag submission on challenge {challenge_id} [SECURITY_MONITORED]"
                elif event_type == 'flag_submission_failure':
                    log_msg = "[{date}] SECURITY: {name} (IP: {ip}) failed flag submission on challenge {challenge_id} (length: {submission_length}) [SECURITY_MONITORED]"
                elif event_type == 'flag_submission_ratelimited':
                    log_msg = "[{date}] SECURITY: {name} (IP: {ip}) rate-limited flag submission on challenge {challenge_id} [SECURITY_THREAT]"
                elif event_type == 'flag_submission_forbidden':
                    log_msg = "[{date}] SECURITY: {name} (IP: {ip}) forbidden flag submission on challenge {challenge_id} [SECURITY_THREAT]"
                else:
                    log_msg = "[{date}] SECURITY: {name} (IP: {ip}) {event_type} on challenge {challenge_id} [SECURITY_EVENT]"

                # Log to CTFd's submissions logger
                log(
                    "submissions",
                    log_msg,
                    challenge_id=challenge_id,
                    event_type=event_type,
                    severity=severity,
                    submission_length=submission_length,
                    status_code=status_code,
                    security_event=True
                )

            # Log high/critical security events to main app logger
            if severity in ['high', 'critical']:
                if current_app:
                    current_app.logger.warning(
                        f"SECURITY [{severity.upper()}]: {event_type} from {source_ip} "
                        f"(User: {user_id or 'anonymous'}) - {details}"
                    )

        except Exception as e:
            if current_app:
                current_app.logger.error(f"Failed to log to CTFd native logging: {str(e)}")

    def _create_alert(self, alert_type, severity, title, description, source_ip=None, user_id=None):
        """Create a security alert"""
        try:
            # Check if similar alert already exists within the last hour
            one_hour_ago = datetime.utcnow() - timedelta(hours=1)
            existing_alert = SecurityAlert.query.filter_by(
                alert_type=alert_type,
                source_ip=source_ip,
                status='active'
            ).filter(
                SecurityAlert.created_at >= one_hour_ago
            ).first()

            if existing_alert:
                # Update existing alert
                existing_alert.event_count += 1
                existing_alert.updated_at = datetime.utcnow()
                existing_alert.description = description
            else:
                # Create new alert
                alert = SecurityAlert(
                    alert_type=alert_type,
                    severity=severity,
                    title=title,
                    description=description,
                    source_ip=source_ip,
                    user_id=user_id
                )
                db.session.add(alert)

            db.session.commit()
        except Exception as e:
            db.session.rollback()
            if current_app:
                current_app.logger.error(f"Failed to create security alert: {str(e)}")

    def _check_alert_conditions(self, event):
        """Check if event should trigger an alert"""
        # High severity events always create alerts
        if event.severity == 'critical':
            self._create_alert(
                event.event_type,
                'critical',
                f'Critical security event: {event.event_type}',
                f'Critical security event detected from {event.source_ip}',
                event.source_ip,
                event.user_id
            )

        # Check for repeated events from same IP
        recent_events = SecurityEvent.query.filter(
            SecurityEvent.source_ip == event.source_ip,
            SecurityEvent.event_type == event.event_type,
            SecurityEvent.timestamp >= datetime.utcnow() - timedelta(minutes=10)
        ).count()

        if recent_events >= 5:  # 5 similar events in 10 minutes
            self._create_alert(
                f'repeated_{event.event_type}',
                'high',
                f'Repeated {event.event_type} from {event.source_ip}',
                f'IP {event.source_ip} triggered {recent_events} {event.event_type} events in 10 minutes',
                event.source_ip,
                event.user_id
            )

    def _check_auto_ban(self, source_ip, event_type):
        """Enhanced progressive penalty system with tiered responses"""
        if not SecurityConfig.get_config('security_auto_ban_enabled', False):
            return

        # Progressive penalty thresholds
        warning_threshold = SecurityConfig.get_config('security_warning_threshold', 3)
        temp_ban_threshold = SecurityConfig.get_config('security_temp_ban_threshold', 7)
        perm_ban_threshold = SecurityConfig.get_config('security_perm_ban_threshold', 15)

        # Count events in different time windows for progressive response
        recent_events_5m = SecurityEvent.query.filter(
            SecurityEvent.source_ip == source_ip,
            SecurityEvent.severity.in_(['high', 'critical']),
            SecurityEvent.timestamp >= datetime.utcnow() - timedelta(minutes=5)
        ).count()

        recent_events_1h = SecurityEvent.query.filter(
            SecurityEvent.source_ip == source_ip,
            SecurityEvent.severity.in_(['high', 'critical']),
            SecurityEvent.timestamp >= datetime.utcnow() - timedelta(hours=1)
        ).count()

        # Check if already banned
        if SecurityBan.is_banned(source_ip):
            return

        try:
            # Level 1: Warning (3+ events in 5 minutes)
            if recent_events_5m >= warning_threshold:
                self._create_alert(
                    'security_warning',
                    'medium',
                    f'Security Warning for IP: {source_ip}',
                    f'IP {source_ip} triggered {recent_events_5m} security events in 5 minutes',
                    source_ip
                )

            # Level 2: Temporary ban (7+ events in 1 hour)
            if recent_events_1h >= temp_ban_threshold:
                ban = SecurityBan(
                    ip_address=source_ip,
                    reason=f'Temporary auto-ban: {recent_events_1h} high/critical events in 1 hour',
                    ban_type='temporary',
                    expires_at=datetime.utcnow() + timedelta(hours=2)  # 2 hour ban
                )
                db.session.add(ban)
                db.session.commit()

                self._create_alert(
                    'temp_auto_ban',
                    'high',
                    f'Temporary Auto-ban: {source_ip}',
                    f'IP {source_ip} temporarily banned for 2 hours after {recent_events_1h} security events',
                    source_ip
                )

            # Level 3: Permanent ban (15+ events in 1 hour)
            elif recent_events_1h >= perm_ban_threshold:
                ban = SecurityBan(
                    ip_address=source_ip,
                    reason=f'Permanent auto-ban: {recent_events_1h} high/critical events in 1 hour',
                    ban_type='permanent',
                    expires_at=None
                )
                db.session.add(ban)
                db.session.commit()

                self._create_alert(
                    'perm_auto_ban',
                    'critical',
                    f'PERMANENT Auto-ban: {source_ip}',
                    f'IP {source_ip} permanently banned after {recent_events_1h} security events',
                    source_ip
                )

        except Exception as e:
            db.session.rollback()
            if current_app:
                current_app.logger.error(f"Failed to process auto-ban for IP {source_ip}: {str(e)}")

    def check_geographic_threats(self, source_ip, country_code=None):
        """Enhanced geographic threat detection"""
        if not country_code:
            return False
            
        # High-risk countries (customize based on your threat landscape)
        high_risk_countries = SecurityConfig.get_config('high_risk_countries', ['CN', 'RU', 'KP', 'IR'])
        
        if country_code in high_risk_countries:
            # Count recent events from this country
            recent_country_events = SecurityEvent.query.filter(
                SecurityEvent.details.contains(f'"country":"{country_code}"'),
                SecurityEvent.timestamp >= datetime.utcnow() - timedelta(hours=1)
            ).count()
            
            if recent_country_events > 50:  # High volume from risky country
                self._create_alert(
                    'geographic_threat',
                    'high',
                    f'High-risk geographic activity: {country_code}',
                    f'{recent_country_events} security events from {country_code} in last hour',
                    source_ip
                )
                return True
                
        return False
