# Simple Dashboard Plugin - Configuration Documentation

## Overview
This plugin replaces the complex security monitoring system with two simple dashboards for CTFd log visualization.

## Features
- **General Dashboard**: Shows CTFd overview statistics, user activity, challenge popularity
- **Security Dashboard**: Shows failed attempts, suspicious activity patterns, submission timelines

## What was removed/simplified:
1. **Complex Security Models**: No custom database tables (SecurityEvent, SecurityConfig, etc.)
2. **Real-time Monitoring**: No before/after request hooks affecting performance  
3. **Threat Detection**: No complex algorithms for threat scoring or IP reputation
4. **Rate Limiting**: No automatic blocking or middleware interference
5. **Custom Alerting**: No email/webhook notifications or alert management
6. **Geographic Blocking**: No country-based IP filtering
7. **Auto-banning**: No automatic user/IP banning systems

## What it provides:
1. **Clean Data Visualization**: Uses existing CTFd models (Users, Submissions, Fails, etc.)
2. **Two Simple Dashboards**: 
   - `/plugins/simple_dashboard/general` - General CTFd info
   - `/plugins/simple_dashboard/security` - Security logs visualization
3. **Refresh Functionality**: Manual refresh buttons for updated data
4. **Time Range Filtering**: 1h, 6h, 24h, 7d options for security dashboard
5. **Responsive Design**: Clean Bootstrap-based UI that matches CTFd admin theme

## API Endpoints:
- `/plugins/simple_dashboard/api/general-stats` - General statistics JSON
- `/plugins/simple_dashboard/api/security-stats` - Security statistics JSON

## No Database Migrations Required
This plugin uses only existing CTFd tables, avoiding the migration conflicts that plagued the previous security plugins.

## Performance Impact: Minimal
- No middleware hooks
- No real-time monitoring
- Simple database queries only when dashboard is accessed
- No background processes or data collection services

## Installation
The plugin is automatically loaded when CTFd starts. Access via Admin menu:
- "📊 General Dashboard" 
- "🔒 Security Logs"
