{% extends "admin/base.html" %}

{% block content %}
<div class="jumbotron">
    <div class="container">
        <h1>📋 Security Events</h1>
        <p>Detailed view of all security events and incidents</p>
    </div>
</div>

<div class="container">
    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row">
                <div class="col-md-3">
                    <select name="type" class="form-control">
                        <option value="">All Event Types</option>
                        <option value="rate_limit_exceeded" {% if request.args.get('type') == 'rate_limit_exceeded' %}selected{% endif %}>Rate Limit Exceeded</option>
                        <option value="ddos_detected" {% if request.args.get('type') == 'ddos_detected' %}selected{% endif %}>DDoS Detected</option>
                        <option value="suspicious_activity" {% if request.args.get('type') == 'suspicious_activity' %}selected{% endif %}>Suspicious Activity</option>
                        <option value="auto_ban" {% if request.args.get('type') == 'auto_ban' %}selected{% endif %}>Auto Ban</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select name="severity" class="form-control">
                        <option value="">All Severities</option>
                        <option value="low" {% if request.args.get('severity') == 'low' %}selected{% endif %}>Low</option>
                        <option value="medium" {% if request.args.get('severity') == 'medium' %}selected{% endif %}>Medium</option>
                        <option value="high" {% if request.args.get('severity') == 'high' %}selected{% endif %}>High</option>
                        <option value="critical" {% if request.args.get('severity') == 'critical' %}selected{% endif %}>Critical</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <input type="text" name="ip" class="form-control" placeholder="Source IP" value="{{ request.args.get('ip', '') }}">
                </div>
                <div class="col-md-3">
                    <button type="submit" class="btn btn-primary">Filter</button>
                    <a href="/plugins/security_monitor/admin/events" class="btn btn-secondary">Clear</a>
                </div>
            </form>
        </div>
    </div>

    <!-- Events Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5>Security Events</h5>
            <div>
                <span class="text-muted">Total: {{ events.total }} events</span>
            </div>
        </div>
        <div class="card-body">
            {% if events.items %}
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Timestamp</th>
                            <th>Type</th>
                            <th>Severity</th>
                            <th>Source IP</th>
                            <th>User</th>
                            <th>Endpoint</th>
                            <th>Method</th>
                            <th>Details</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for event in events.items %}
                        <tr>
                            <td>{{ event.id }}</td>
                            <td>{{ event.timestamp.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                            <td>
                                <span class="badge badge-secondary">{{ event.event_type }}</span>
                            </td>
                            <td>
                                <span class="badge badge-{% if event.severity == 'critical' %}danger{% elif event.severity == 'high' %}warning{% elif event.severity == 'medium' %}info{% else %}secondary{% endif %}">
                                    {{ event.severity.upper() }}
                                </span>
                            </td>
                            <td>
                                <code>{{ event.source_ip }}</code>
                                <button class="btn btn-sm btn-outline-primary ml-1" onclick="showIPDetails('{{ event.source_ip }}')">
                                    <i class="fas fa-info-circle"></i>
                                </button>
                            </td>
                            <td>
                                {% if event.user_id %}
                                <a href="/admin/users/{{ event.user_id }}">User {{ event.user_id }}</a>
                                {% else %}
                                <span class="text-muted">Anonymous</span>
                                {% endif %}
                            </td>
                            <td>{{ event.endpoint or '-' }}</td>
                            <td>{{ event.method or '-' }}</td>
                            <td>
                                {% if event.details %}
                                <button class="btn btn-sm btn-outline-info" onclick="showEventDetails({{ event.id }}, {{ event.details | tojson }})">
                                    View Details
                                </button>
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-sm btn-outline-warning" onclick="banIP('{{ event.source_ip }}')">
                                        Ban IP
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteEvent({{ event.id }})">
                                        Delete
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if events.pages > 1 %}
            <nav aria-label="Events pagination">
                <ul class="pagination justify-content-center">
                    {% if events.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('security_monitor_admin.events', page=events.prev_num, **request.args) }}">Previous</a>
                    </li>
                    {% endif %}

                    {% for page_num in events.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != events.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('security_monitor_admin.events', page=page_num, **request.args) }}">{{ page_num }}</a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                        {% endif %}
                    {% endfor %}

                    {% if events.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('security_monitor_admin.events', page=events.next_num, **request.args) }}">Next</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
            {% else %}
            <div class="text-center py-4">
                <p class="text-muted">No security events found</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Event Details Modal -->
<div class="modal fade" id="eventDetailsModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Event Details</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <pre id="eventDetailsContent"></pre>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- IP Details Modal -->
<div class="modal fade" id="ipDetailsModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">IP Address Details</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="ipDetailsContent">
                Loading...
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
function showEventDetails(eventId, details) {
    document.getElementById('eventDetailsContent').textContent = JSON.stringify(details, null, 2);
    $('#eventDetailsModal').modal('show');
}

function showIPDetails(ip) {
    document.getElementById('ipDetailsContent').innerHTML = 'Loading...';
    $('#ipDetailsModal').modal('show');

    // Fetch IP details
    fetch(`/api/v1/plugins/security_monitor/ip-details/${ip}`)
        .then(response => response.json())
        .then(data => {
            let html = `
                <p><strong>IP Address:</strong> ${ip}</p>
                <p><strong>Total Events:</strong> ${data.total_events || 0}</p>
                <p><strong>Last Seen:</strong> ${data.last_seen || 'Never'}</p>
                <p><strong>Is Banned:</strong> ${data.is_banned ? 'Yes' : 'No'}</p>
                <p><strong>Event Types:</strong></p>
                <ul>
            `;

            if (data.event_types) {
                for (let [type, count] of Object.entries(data.event_types)) {
                    html += `<li>${type}: ${count}</li>`;
                }
            }

            html += '</ul>';
            document.getElementById('ipDetailsContent').innerHTML = html;
        })
        .catch(error => {
            document.getElementById('ipDetailsContent').innerHTML = 'Error loading IP details';
        });
}

function banIP(ip) {
    if (confirm(`Are you sure you want to ban IP ${ip}?`)) {
        fetch('/api/v1/plugins/security_monitor/ban-ip', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'CSRF-Token': "{{ nonce }}"
            },
            body: JSON.stringify({
                ip_address: ip,
                reason: 'Manual ban from security events',
                ban_type: 'temporary',
                duration_hours: 24
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('IP banned successfully');
                location.reload();
            } else {
                alert('Error banning IP: ' + data.message);
            }
        });
    }
}

function deleteEvent(eventId) {
    if (confirm('Are you sure you want to delete this event?')) {
        fetch(`/api/v1/plugins/security_monitor/events/${eventId}`, {
            method: 'DELETE',
            headers: {
                'CSRF-Token': "{{ nonce }}"
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error deleting event: ' + data.message);
            }
        });
    }
}
</script>
{% endblock %}
