import os
import json
import time
import logging
from datetime import datetime, timedelta
from flask import Blueprint, render_template, request, jsonify, current_app, session, abort
from flask_restx import Namespace, Resource

from CTFd.models import db, Users, Challenges, Submissions, Fails
from CTFd.plugins import (
    register_plugin_assets_directory,
    register_admin_plugin_menu_bar,
    bypass_csrf_protection
)
from CTFd.utils.decorators import authed_only, admins_only
from CTFd.utils import get_config, set_config, user as current_user
from CTFd.utils.user import get_ip
from CTFd.utils.security.csrf import generate_nonce
from CTFd.api import CTFd_API_v1
from CTFd.cache import cache

from .models import SecurityEvent, SecurityConfig, SecurityAlert, SecurityBan, SecurityMetrics, PlatformAnalytics, ContainerMetrics, PerformanceMetrics, create_all
from .utils.detector import SecurityDetector
from .utils.monitor import SecurityMonitor
from .utils.alerting import AlertManager
from .utils.analytics import analytics_collector
from .utils.data_collector import data_collector

def load(app):
    """Load the security monitoring plugin"""
    plugin_name = __name__.split('.')[-1]

    # Initialize database tables
    with app.app_context():
        create_all()

    # Initialize security components
    app.security_detector = SecurityDetector()
    app.security_monitor = SecurityMonitor()
    app.alert_manager = AlertManager()
    app.analytics_collector = analytics_collector
    app.data_collector = data_collector

    # Create local references for use in routes
    security_detector = app.security_detector
    security_monitor = app.security_monitor
    alert_manager = app.alert_manager

    # Start data collection service
    data_collector.start()

    # Register plugin assets
    register_plugin_assets_directory(
        app, base_path=f"/plugins/{plugin_name}/assets",
        endpoint=f'plugins.{plugin_name}.assets'
    )

    # Create blueprint for admin interface
    admin_blueprint = Blueprint(
        "security_monitor_admin",
        __name__,
        template_folder="templates",
        static_folder="assets",
        url_prefix="/plugins/security_monitor/admin"
    )

    # Create API namespace
    security_namespace = Namespace(
        "security_monitor",
        description="Security Monitoring API",
        path="/plugins/security_monitor"
    )

    @admin_blueprint.route('/')
    @admin_blueprint.route('/dashboard')
    @admins_only
    def dashboard():
        """Security monitoring dashboard"""
        # Get recent security events
        recent_events = SecurityEvent.query.order_by(
            SecurityEvent.timestamp.desc()
        ).limit(50).all()

        # Get security statistics
        stats = security_monitor.get_security_stats()

        # Get active alerts
        active_alerts = SecurityAlert.query.filter_by(
            status='active'
        ).order_by(SecurityAlert.created_at.desc()).all()

        return render_template(
            'security_dashboard.html',
            events=recent_events,
            stats=stats,
            alerts=active_alerts,
            nonce=generate_nonce()
        )

    @admin_blueprint.route('/config', methods=['GET', 'POST'])
    @admins_only
    def config():
        """Security configuration panel"""
        if request.method == 'POST':
            data = request.get_json() or request.form

            # Update security configuration
            for key, value in data.items():
                if key.startswith('security_'):
                    SecurityConfig.set_config(key, value)

            return jsonify({'success': True, 'message': 'Configuration updated'})

        # Get current configuration
        config_data = SecurityConfig.get_all_configs()
        return render_template('security_config.html', config=config_data, nonce=generate_nonce())

    @admin_blueprint.route('/events')
    @admins_only
    def events():
        """Security events listing"""
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        event_type = request.args.get('type', '')

        query = SecurityEvent.query
        if event_type:
            query = query.filter_by(event_type=event_type)

        events = query.order_by(
            SecurityEvent.timestamp.desc()
        ).paginate(
            page=page, per_page=per_page, error_out=False
        )

        return render_template('security_events.html', events=events, nonce=generate_nonce())

    @admin_blueprint.route('/alerts')
    @admins_only
    def alerts():
        """Security alerts management"""
        alerts = SecurityAlert.query.order_by(
            SecurityAlert.created_at.desc()
        ).all()

        return render_template('security_alerts.html', alerts=alerts, nonce=generate_nonce())

    # API Endpoints
    @security_namespace.route('/stats')
    class SecurityStats(Resource):
        @admins_only
        def get(self):
            """Get security statistics"""
            return security_monitor.get_security_stats()

    @security_namespace.route('/events')
    class SecurityEvents(Resource):
        @admins_only
        def get(self):
            """Get recent security events"""
            limit = request.args.get('limit', 100, type=int)
            events = SecurityEvent.query.order_by(
                SecurityEvent.timestamp.desc()
            ).limit(limit).all()

            return {
                'events': [event.to_dict() for event in events]
            }

        @admins_only
        def post(self):
            """Create a security event"""
            data = request.get_json()
            event = SecurityEvent(
                event_type=data.get('event_type'),
                severity=data.get('severity', 'medium'),
                source_ip=data.get('source_ip', get_ip()),
                user_id=data.get('user_id'),
                details=data.get('details', {}),
                timestamp=datetime.utcnow()
            )
            db.session.add(event)
            db.session.commit()

            return {'success': True, 'event_id': event.id}

    @security_namespace.route('/alerts/<int:alert_id>')
    class SecurityAlertResource(Resource):
        @admins_only
        def patch(self, alert_id):
            """Update alert status"""
            alert = SecurityAlert.query.get_or_404(alert_id)
            data = request.get_json()

            if 'status' in data:
                alert.status = data['status']
                alert.updated_at = datetime.utcnow()
                if data['status'] == 'resolved':
                    alert.resolved_at = datetime.utcnow()
                    alert.resolved_by = current_user.get_current_user().id if current_user.authed() else None
                db.session.commit()

            return {'success': True}

        @admins_only
        def delete(self, alert_id):
            """Delete alert"""
            alert = SecurityAlert.query.get_or_404(alert_id)
            db.session.delete(alert)
            db.session.commit()
            return {'success': True}

    @security_namespace.route('/alerts/bulk-update')
    class SecurityAlertsBulkUpdate(Resource):
        @admins_only
        def patch(self):
            """Bulk update alerts"""
            data = request.get_json()
            alert_ids = data.get('alert_ids', [])
            status = data.get('status')

            if not alert_ids or not status:
                return {'success': False, 'message': 'Missing alert_ids or status'}

            alerts = SecurityAlert.query.filter(SecurityAlert.id.in_(alert_ids)).all()
            for alert in alerts:
                alert.status = status
                alert.updated_at = datetime.utcnow()
                if status == 'resolved':
                    alert.resolved_at = datetime.utcnow()
                    alert.resolved_by = current_user.get_current_user().id if current_user.authed() else None

            db.session.commit()
            return {'success': True, 'updated': len(alerts)}

    @security_namespace.route('/alerts/bulk-delete')
    class SecurityAlertsBulkDelete(Resource):
        @admins_only
        def delete(self):
            """Bulk delete alerts"""
            data = request.get_json()
            alert_ids = data.get('alert_ids', [])

            if not alert_ids:
                return {'success': False, 'message': 'Missing alert_ids'}

            deleted = SecurityAlert.query.filter(SecurityAlert.id.in_(alert_ids)).delete()
            db.session.commit()
            return {'success': True, 'deleted': deleted}

    @security_namespace.route('/alerts/resolve-all')
    class SecurityAlertsResolveAll(Resource):
        @admins_only
        def post(self):
            """Resolve all active alerts"""
            alerts = SecurityAlert.query.filter_by(status='active').all()
            for alert in alerts:
                alert.status = 'resolved'
                alert.updated_at = datetime.utcnow()
                alert.resolved_at = datetime.utcnow()
                alert.resolved_by = current_user.get_current_user().id if current_user.authed() else None

            db.session.commit()
            return {'success': True, 'resolved': len(alerts)}

    @security_namespace.route('/alerts/delete-resolved')
    class SecurityAlertsDeleteResolved(Resource):
        @admins_only
        def delete(self):
            """Delete all resolved alerts"""
            deleted = SecurityAlert.query.filter_by(status='resolved').delete()
            db.session.commit()
            return {'success': True, 'deleted': deleted}

    @security_namespace.route('/ip-details/<ip>')
    class SecurityIPDetails(Resource):
        @admins_only
        def get(self, ip):
            """Get details for an IP address"""
            # Get event statistics for this IP
            events = SecurityEvent.query.filter_by(source_ip=ip).all()

            event_types = {}
            for event in events:
                event_types[event.event_type] = event_types.get(event.event_type, 0) + 1

            last_event = SecurityEvent.query.filter_by(source_ip=ip).order_by(
                SecurityEvent.timestamp.desc()
            ).first()

            return {
                'ip': ip,
                'total_events': len(events),
                'event_types': event_types,
                'last_seen': last_event.timestamp.isoformat() if last_event else None,
                'is_banned': SecurityBan.is_banned(ip)
            }

    @security_namespace.route('/ban-ip')
    class SecurityBanIP(Resource):
        @admins_only
        def post(self):
            """Ban an IP address"""
            data = request.get_json()

            ip_address = data.get('ip_address')
            reason = data.get('reason', 'Manual ban')
            ban_type = data.get('ban_type', 'temporary')
            duration_hours = data.get('duration_hours', 24)

            if not ip_address:
                return {'success': False, 'message': 'IP address required'}

            # Check if already banned
            if SecurityBan.is_banned(ip_address):
                return {'success': False, 'message': 'IP already banned'}

            expires_at = None
            if ban_type == 'temporary':
                expires_at = datetime.utcnow() + timedelta(hours=duration_hours)

            ban = SecurityBan(
                ip_address=ip_address,
                reason=reason,
                ban_type=ban_type,
                expires_at=expires_at,
                created_by=current_user.get_current_user().id if current_user.authed() else None
            )

            db.session.add(ban)
            db.session.commit()

            return {'success': True, 'ban_id': ban.id}

    @security_namespace.route('/test-alerts')
    class SecurityTestAlerts(Resource):
        @admins_only
        def post(self):
            """Test alert channels"""
            return alert_manager.test_alert_channels()

    @security_namespace.route('/analytics/platform')
    class PlatformAnalyticsAPI(Resource):
        @admins_only
        def get(self):
            """Get platform analytics data"""
            hours = request.args.get('hours', 24, type=int)
            category = request.args.get('category', None)

            since = datetime.utcnow() - timedelta(hours=hours)
            query = PlatformAnalytics.query.filter(PlatformAnalytics.timestamp >= since)

            if category:
                query = query.filter(PlatformAnalytics.metric_category == category)

            analytics = query.order_by(PlatformAnalytics.timestamp.desc()).all()

            return {
                'analytics': [metric.to_dict() for metric in analytics],
                'total_count': len(analytics)
            }

    @security_namespace.route('/analytics/containers')
    class ContainerAnalyticsAPI(Resource):
        @admins_only
        def get(self):
            """Get container analytics data"""
            hours = request.args.get('hours', 24, type=int)
            container_type = request.args.get('type', None)

            since = datetime.utcnow() - timedelta(hours=hours)
            query = ContainerMetrics.query.filter(ContainerMetrics.timestamp >= since)

            if container_type:
                query = query.filter(ContainerMetrics.container_type == container_type)

            metrics = query.order_by(ContainerMetrics.timestamp.desc()).all()

            return {
                'metrics': [metric.to_dict() for metric in metrics],
                'total_count': len(metrics)
            }

    @security_namespace.route('/analytics/performance')
    class PerformanceAnalyticsAPI(Resource):
        @admins_only
        def get(self):
            """Get performance analytics data"""
            hours = request.args.get('hours', 24, type=int)
            component = request.args.get('component', None)

            since = datetime.utcnow() - timedelta(hours=hours)
            query = PerformanceMetrics.query.filter(PerformanceMetrics.timestamp >= since)

            if component:
                query = query.filter(PerformanceMetrics.component == component)

            metrics = query.order_by(PerformanceMetrics.timestamp.desc()).all()

            return {
                'metrics': [metric.to_dict() for metric in metrics],
                'total_count': len(metrics)
            }

    @security_namespace.route('/analytics/summary')
    class AnalyticsSummaryAPI(Resource):
        @admins_only
        def get(self):
            """Get analytics summary for dashboard"""
            hours = request.args.get('hours', 24, type=int)
            since = datetime.utcnow() - timedelta(hours=hours)

            # Platform metrics summary
            platform_metrics = PlatformAnalytics.query.filter(
                PlatformAnalytics.timestamp >= since
            ).count()

            # Container metrics summary
            container_metrics = ContainerMetrics.query.filter(
                ContainerMetrics.timestamp >= since
            ).count()

            # Performance metrics summary
            performance_metrics = PerformanceMetrics.query.filter(
                PerformanceMetrics.timestamp >= since
            ).count()

            # Recent container actions
            recent_container_actions = ContainerMetrics.query.filter(
                ContainerMetrics.timestamp >= since
            ).group_by(ContainerMetrics.action).all()

            container_action_counts = {}
            for metric in recent_container_actions:
                action = metric.action
                count = ContainerMetrics.query.filter(
                    ContainerMetrics.timestamp >= since,
                    ContainerMetrics.action == action
                ).count()
                container_action_counts[action] = count

            return {
                'summary': {
                    'platform_metrics_count': platform_metrics,
                    'container_metrics_count': container_metrics,
                    'performance_metrics_count': performance_metrics,
                    'container_actions': container_action_counts,
                    'time_range_hours': hours
                }
            }

    # Monitor real CTFd events efficiently
    @app.after_request
    def monitor_ctfd_events(response):
        """Monitor key CTFd events"""
        ip = get_ip()
        user_id = session.get('id')
        detector = current_app.security_detector

        # Authentication events
        if request.endpoint == 'auth.login' and request.method == 'POST':
            if response.status_code == 200 and user_id:
                detector.log_security_event('login_success', 'info', ip, user_id,
                    {'endpoint': 'auth.login', 'user_agent': request.headers.get('User-Agent', '')[:100]})
            elif response.status_code in [401, 403]:
                detector.log_security_event('login_failure', 'medium', ip, None,
                    {'endpoint': 'auth.login', 'attempted_user': request.form.get('name', '')[:50]})

        # Registration events
        elif request.endpoint == 'auth.register' and request.method == 'POST' and response.status_code == 200:
            detector.log_security_event('user_registration', 'info', ip, session.get('id'),
                {'endpoint': 'auth.register', 'email': request.form.get('email', '')[:50]})

        # Flag submission events
        elif (request.endpoint == 'api.challenges_challenge_attempt' or
              'attempt' in str(request.endpoint)) and request.method == 'POST':

            # Determine result from response
            try:
                if response.status_code == 200:
                    response_data = response.get_json() if hasattr(response, 'get_json') else {}
                    status = response_data.get('data', {}).get('status', 'unknown')
                    if status == 'correct':
                        event_type, severity = 'flag_submission_success', 'low'
                    elif status == 'incorrect':
                        event_type, severity = 'flag_submission_failure', 'low'
                    elif status == 'ratelimited':
                        event_type, severity = 'flag_submission_ratelimited', 'high'
                    else:
                        event_type, severity = 'flag_submission_unknown', 'medium'
                elif response.status_code == 429:
                    event_type, severity = 'flag_submission_ratelimited', 'high'
                else:
                    event_type, severity = 'flag_submission_error', 'medium'

                challenge_id = request.get_json().get('challenge_id') if request.content_type == 'application/json' else request.form.get('challenge_id')
                detector.log_security_event(event_type, severity, ip, user_id,
                    {'challenge_id': challenge_id, 'status_code': response.status_code})

            except Exception:
                pass  # Fail silently to not break CTFd

        return response

    # Enhanced real-time security monitoring
    @app.before_request
    def security_monitor_middleware():
        """Comprehensive real-time security monitoring"""
        # Skip static assets only
        if request.endpoint in ['static'] or request.path.startswith(('/themes/', '/assets/')):
            return

        ip = get_ip()
        user_id = session.get('id')
        endpoint = request.endpoint or 'unknown'
        detector = current_app.security_detector

        # Log ALL requests for comprehensive monitoring
        detector.log_security_event(
            'http_request',
            'info',
            ip,
            user_id,
            {
                'endpoint': endpoint,
                'method': request.method,
                'path': request.path,
                'user_agent': request.headers.get('User-Agent', '')[:200],
                'real_time': True
            }
        )

        # Check if IP is banned
        if SecurityBan.is_banned(ip):
            detector.log_security_event(
                'banned_ip_access',
                'critical',
                ip,
                user_id,
                {'endpoint': endpoint, 'method': request.method, 'blocked': True}
            )
            return jsonify({'error': 'Access denied', 'message': 'IP banned'}), 403

        # Real-time rate limiting with logging
        if detector.check_rate_limit(ip, endpoint):
            detector.log_security_event(
                'rate_limit_exceeded',
                'high',
                ip,
                user_id,
                {'endpoint': endpoint, 'method': request.method, 'blocked': True}
            )
            return jsonify({'error': 'Rate limit exceeded'}), 429

        # Real-time DDoS detection with logging
        if detector.detect_ddos(ip):
            detector.log_security_event(
                'ddos_detected',
                'critical',
                ip,
                user_id,
                {'endpoint': endpoint, 'method': request.method, 'blocked': True}
            )
            return jsonify({'error': 'DDoS detected'}), 403

        # Real-time suspicious activity detection
        if detector.detect_suspicious_activity(ip, endpoint, request):
            detector.log_security_event(
                'suspicious_activity',
                'medium',
                ip,
                user_id,
                {
                    'endpoint': endpoint,
                    'method': request.method,
                    'user_agent': request.headers.get('User-Agent', '')[:200],
                    'blocked': SecurityConfig.get_config('security_block_suspicious_activity', False)
                }
            )

            # Block if configured to do so
            if SecurityConfig.get_config('security_block_suspicious_activity', False):
                return jsonify({'error': 'Suspicious activity detected'}), 403

    # Register blueprints and API
    app.register_blueprint(admin_blueprint)
    CTFd_API_v1.add_namespace(security_namespace, path="/plugins/security_monitor")

    # Register admin menu
    register_admin_plugin_menu_bar(
        "Security Monitor",
        "/plugins/security_monitor/admin/"
    )

    # Initialize default configuration
    if not SecurityConfig.get_config('initialized'):
        _initialize_default_config()
        SecurityConfig.set_config('initialized', True)

    print(f" * Loaded Security Monitor plugin")

def _initialize_default_config():
    """Initialize enhanced security configuration with better defaults"""
    defaults = {
        # Enhanced rate limiting
        'security_rate_limit_enabled': True,
        'security_rate_limit_requests': 50,  # More restrictive default
        'security_rate_limit_window': 60,
        
        # Enhanced DDoS detection
        'security_ddos_detection_enabled': True,
        'security_ddos_threshold': 300,  # Lower threshold for better detection
        'security_ddos_window': 60,
        
        # Enhanced suspicious activity detection
        'security_suspicious_activity_enabled': True,
        'security_block_suspicious_activity': True,  # Enable blocking by default
        'security_suspicious_threshold': 8,  # Lower threshold
        
        # Progressive penalty system
        'security_auto_ban_enabled': True,  # Enable by default
        'security_warning_threshold': 3,    # Warning after 3 events in 5min
        'security_temp_ban_threshold': 7,   # Temp ban after 7 events in 1h
        'security_perm_ban_threshold': 15,  # Perm ban after 15 events in 1h
        
        # Geographic security
        'high_risk_countries': ['CN', 'RU', 'KP', 'IR', 'BY'],
        'geographic_blocking_enabled': False,  # Disable by default (may block legitimate users)
        
        # Alert configuration
        'security_email_alerts_enabled': False,
        'security_webhook_alerts_enabled': False,
        'security_alert_cooldown': 300,  # 5 minutes between similar alerts
        
        # Data retention
        'security_log_retention_days': 30,
        'security_metrics_retention_days': 90,
        
        # Advanced settings
        'security_enhanced_logging': True,
        'security_correlation_enabled': True,
        'security_threat_scoring': True,
        
        # Competition-specific settings
        'ctf_protection_mode': 'balanced',  # Options: 'permissive', 'balanced', 'strict'
        'flag_brute_force_protection': True,
        'challenge_ddos_protection': True,
        
        # Emergency response
        'emergency_lockdown_enabled': False,
        'emergency_contact_email': '',
        'emergency_response_webhook': ''
    }

    for key, value in defaults.items():
        if not SecurityConfig.get_config(key):
            SecurityConfig.set_config(key, value)
