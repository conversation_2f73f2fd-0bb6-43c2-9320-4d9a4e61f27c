<div class="tab-pane fade show active" id="docker" role="tabpanel" aria-autocomplete="none">
    <h5>Common</h5>
    <small class="form-text text-muted">
        Common configurations for both standalone and grouped containers
    </small><br>
    {% for config, val in {
        "API URL": ("docker_api_url", "Docker API to connect to"),
        "Credentials": ("docker_credentials", "docker.io username and password, separated by ':'. useful for private images"),
        "Swarm Nodes": ("docker_swarm_nodes", "Will pick up one from it, You should set your node with label name=windows-* or name=linux-*. Separated by commas."),
    }.items() %}
        {% set value = get_config('whale:' + val[0]) %}
        <div class="form-group">
            <label for="{{ val[0].replace('_', '-') }}">
                {{ config }}
                <small class="form-text text-muted">
                    {{ val[1] }}
                </small>
            </label>
            <input type="text" class="form-control"
                   id="{{ val[0].replace('_', '-') }}" name="{{ 'whale:' + val[0] }}"
                   {% if value != None %}value="{{ value }}"{% endif %}>
        </div>
    {% endfor %}
    <hr>
    <h5>Standalone Containers</h5>
    <small class="form-text text-muted">
        Typical challenges. Under most circumstances you only need to set these.
    </small><br>
    {% for config, val in {
        "Auto Connect Network": ("docker_auto_connect_network", "The network connected for single-containers. It's usually the same network as the frpc is in."),
        "Dns Setting": ("docker_dns", "Decide which dns will be used in container network."),
    }.items() %}
        {% set value = get_config('whale:' + val[0]) %}
        <div class="form-group">
            <label for="{{ val[0].replace('_', '-') }}">
                {{ config }}
                <small class="form-text text-muted">
                    {{ val[1] }}
                </small>
            </label>
            <input type="text" class="form-control"
                   id="{{ val[0].replace('_', '-') }}" name="{{ 'whale:' + val[0] }}"
                   {% if value != None %}value="{{ value }}"{% endif %}>
        </div>
    {% endfor %}
    <hr>
    <h5>Grouped Containers</h5>
    <small class="form-text text-muted">
        Designed for multi-container challenges
    </small><br>
    {% for config, val in {
        "Auto Connect Containers": ("docker_auto_connected_containers","Decide which container will be connected to multi-container-network automatically. Separated by commas."),
        "Multi-Container Network Subnet": ("docker_subnet", "Subnet which will be used by auto created networks for multi-container challenges."),
        "Multi-Container Network Subnet New Prefix": ("docker_subnet_new_prefix", "Prefix for auto created network.")
    }.items() %}
        {% set value = get_config('whale:' + val[0]) %}
        <div class="form-group">
            <label for="{{ val[0].replace('_', '-') }}">
                {{ config }}
                <small class="form-text text-muted">
                    {{ val[1] }}
                </small>
            </label>
            <input type="text" class="form-control"
                   id="{{ val[0].replace('_', '-') }}" name="{{ 'whale:' + val[0] }}"
                   {% if value != None %}value="{{ value }}"{% endif %}>
        </div>
    {% endfor %}

    <div class="submit-row float-right">
        <button type="submit" tabindex="0" class="btn btn-md btn-primary btn-outlined">
            Submit
        </button>
    </div>
</div>