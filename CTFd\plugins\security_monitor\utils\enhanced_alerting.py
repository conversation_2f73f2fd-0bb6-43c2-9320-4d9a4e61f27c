"""
Enhanced Alert Manager with Smart Grouping and Threat Intelligence
Reduces alert fatigue and provides actionable security intelligence
"""

import json
import time
import hashlib
import smtplib
import requests
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from email.mime.text import MIMEText
from email.mime.multipart import <PERSON><PERSON><PERSON><PERSON>ip<PERSON>
from flask import current_app
from CTFd.models import db
from CTFd.cache import cache
from ..models import SecurityEvent, SecurityAlert, SecurityConfig, SecurityBan

class EnhancedAlertManager:
    """Enhanced alert management with smart grouping and reduced fatigue"""

    def __init__(self):
        self.alert_grouping_rules = {
            # Group similar alerts by IP and time window
            'ip_based_grouping': {
                'time_window': 300,  # 5 minutes
                'max_alerts_per_ip': 3,
                'similar_event_types': [
                    ['rate_limit_exceeded', 'advanced_rate_limit_exceeded'],
                    ['suspicious_activity', 'advanced_suspicious_activity'],
                    ['ddos_detected', 'advanced_ddos'],
                    ['flag_submission_failure', 'flag_submission_ratelimited']
                ]
            },
            
            # Group alerts by attack patterns
            'pattern_based_grouping': {
                'time_window': 600,  # 10 minutes
                'similar_patterns': [
                    ['sql_injection', 'xss_attempt', 'code_injection'],
                    ['path_traversal', 'file_inclusion'],
                    ['brute_force', 'credential_stuffing'],
                    ['ddos_detected', 'rate_limit_exceeded']
                ]
            },
            
            # Group alerts by severity and create summary alerts
            'severity_based_grouping': {
                'time_window': 900,  # 15 minutes
                'critical_threshold': 5,  # 5+ critical alerts = summary
                'high_threshold': 10     # 10+ high alerts = summary
            }
        }
        
        self.notification_channels = {}
        self._load_notification_config()

    def _load_notification_config(self):
        """Load notification channel configuration"""
        try:
            # Email configuration
            email_config = {
                'enabled': SecurityConfig.get_config('security_email_alerts_enabled', False),
                'smtp_server': SecurityConfig.get_config('smtp_server', 'localhost'),
                'smtp_port': SecurityConfig.get_config('smtp_port', 587),
                'smtp_username': SecurityConfig.get_config('smtp_username', ''),
                'smtp_password': SecurityConfig.get_config('smtp_password', ''),
                'from_email': SecurityConfig.get_config('alert_from_email', '<EMAIL>'),
                'to_emails': SecurityConfig.get_config('alert_to_emails', []),
                'use_tls': SecurityConfig.get_config('smtp_use_tls', True)
            }
            self.notification_channels['email'] = email_config
            
            # Webhook configuration
            webhook_config = {
                'enabled': SecurityConfig.get_config('security_webhook_alerts_enabled', False),
                'webhook_url': SecurityConfig.get_config('alert_webhook_url', ''),
                'webhook_secret': SecurityConfig.get_config('alert_webhook_secret', ''),
                'custom_headers': SecurityConfig.get_config('alert_webhook_headers', {})
            }
            self.notification_channels['webhook'] = webhook_config
            
            # Slack configuration
            slack_config = {
                'enabled': SecurityConfig.get_config('security_slack_alerts_enabled', False),
                'webhook_url': SecurityConfig.get_config('slack_webhook_url', ''),
                'channel': SecurityConfig.get_config('slack_channel', '#security'),
                'username': SecurityConfig.get_config('slack_username', 'CTFd Security Bot')
            }
            self.notification_channels['slack'] = slack_config
            
            # Discord configuration
            discord_config = {
                'enabled': SecurityConfig.get_config('security_discord_alerts_enabled', False),
                'webhook_url': SecurityConfig.get_config('discord_webhook_url', ''),
                'username': SecurityConfig.get_config('discord_username', 'CTFd Security')
            }
            self.notification_channels['discord'] = discord_config
            
        except Exception as e:
            if current_app:
                current_app.logger.error(f"Failed to load notification config: {str(e)}")

    def process_alert(self, alert_type, severity, title, description, source_ip=None, user_id=None, event_details=None):
        """
        Process an alert with smart grouping and deduplication
        Returns True if alert was processed (created or grouped), False if suppressed
        """
        try:
            # Check if alert should be suppressed due to grouping
            grouping_result = self._check_alert_grouping(alert_type, severity, source_ip, event_details)
            
            if grouping_result['action'] == 'suppress':
                return False  # Alert suppressed
            elif grouping_result['action'] == 'group':
                return self._update_grouped_alert(grouping_result['group_id'], alert_type, severity, description)
            elif grouping_result['action'] == 'create_summary':
                return self._create_summary_alert(grouping_result['summary_data'])
            else:  # 'create_new'
                return self._create_new_alert(alert_type, severity, title, description, source_ip, user_id, event_details)
                
        except Exception as e:
            if current_app:
                current_app.logger.error(f"Failed to process alert: {str(e)}")
            return False

    def _check_alert_grouping(self, alert_type, severity, source_ip, event_details):
        """Check if alert should be grouped with existing alerts"""
        current_time = datetime.utcnow()
        
        # Rule 1: IP-based grouping
        if source_ip:
            ip_group_result = self._check_ip_based_grouping(alert_type, source_ip, current_time)
            if ip_group_result['action'] != 'create_new':
                return ip_group_result
        
        # Rule 2: Pattern-based grouping
        pattern_group_result = self._check_pattern_based_grouping(alert_type, current_time)
        if pattern_group_result['action'] != 'create_new':
            return pattern_group_result
        
        # Rule 3: Severity-based summary alerts
        severity_group_result = self._check_severity_based_grouping(severity, current_time)
        if severity_group_result['action'] != 'create_new':
            return severity_group_result
        
        return {'action': 'create_new'}

    def _check_ip_based_grouping(self, alert_type, source_ip, current_time):
        """Check for IP-based alert grouping"""
        time_window = timedelta(seconds=self.alert_grouping_rules['ip_based_grouping']['time_window'])
        max_alerts = self.alert_grouping_rules['ip_based_grouping']['max_alerts_per_ip']
        
        # Find recent alerts from the same IP
        recent_alerts = SecurityAlert.query.filter(
            SecurityAlert.source_ip == source_ip,
            SecurityAlert.created_at >= current_time - time_window,
            SecurityAlert.status == 'active'
        ).all()
        
        if len(recent_alerts) >= max_alerts:
            # Check if we already have a grouped alert for this IP
            existing_group = next((a for a in recent_alerts if a.alert_type == f'grouped_ip_alerts_{source_ip}'), None)
            
            if existing_group:
                return {'action': 'group', 'group_id': existing_group.id}
            else:
                # Create new grouped alert
                return {'action': 'create_summary', 'summary_data': {
                    'type': 'ip_grouped',
                    'source_ip': source_ip,
                    'alert_count': len(recent_alerts) + 1,
                    'alert_types': list(set(a.alert_type for a in recent_alerts))
                }}
        
        # Check for similar event types
        for similar_types in self.alert_grouping_rules['ip_based_grouping']['similar_event_types']:
            if alert_type in similar_types:
                similar_alerts = [a for a in recent_alerts if a.alert_type in similar_types]
                if len(similar_alerts) >= 2:  # Already have 2+ similar alerts
                    return {'action': 'suppress'}  # Suppress this one
        
        return {'action': 'create_new'}

    def _check_pattern_based_grouping(self, alert_type, current_time):
        """Check for pattern-based alert grouping"""
        time_window = timedelta(seconds=self.alert_grouping_rules['pattern_based_grouping']['time_window'])
        
        # Find alerts matching similar patterns
        for pattern_group in self.alert_grouping_rules['pattern_based_grouping']['similar_patterns']:
            if alert_type in pattern_group:
                recent_pattern_alerts = SecurityAlert.query.filter(
                    SecurityAlert.alert_type.in_(pattern_group),
                    SecurityAlert.created_at >= current_time - time_window,
                    SecurityAlert.status == 'active'
                ).count()
                
                if recent_pattern_alerts >= 5:  # 5+ similar pattern alerts
                    return {'action': 'create_summary', 'summary_data': {
                        'type': 'pattern_grouped',
                        'pattern_types': pattern_group,
                        'alert_count': recent_pattern_alerts + 1
                    }}
        
        return {'action': 'create_new'}

    def _check_severity_based_grouping(self, severity, current_time):
        """Check for severity-based alert grouping"""
        time_window = timedelta(seconds=self.alert_grouping_rules['severity_based_grouping']['time_window'])
        
        if severity == 'critical':
            threshold = self.alert_grouping_rules['severity_based_grouping']['critical_threshold']
        elif severity == 'high':
            threshold = self.alert_grouping_rules['severity_based_grouping']['high_threshold']
        else:
            return {'action': 'create_new'}
        
        # Count recent alerts of this severity
        recent_severity_alerts = SecurityAlert.query.filter(
            SecurityAlert.severity == severity,
            SecurityAlert.created_at >= current_time - time_window,
            SecurityAlert.status == 'active'
        ).count()
        
        if recent_severity_alerts >= threshold:
            # Check if we already have a summary alert
            existing_summary = SecurityAlert.query.filter(
                SecurityAlert.alert_type == f'severity_summary_{severity}',
                SecurityAlert.created_at >= current_time - time_window,
                SecurityAlert.status == 'active'
            ).first()
            
            if existing_summary:
                return {'action': 'group', 'group_id': existing_summary.id}
            else:
                return {'action': 'create_summary', 'summary_data': {
                    'type': 'severity_grouped',
                    'severity': severity,
                    'alert_count': recent_severity_alerts + 1
                }}
        
        return {'action': 'create_new'}

    def _create_new_alert(self, alert_type, severity, title, description, source_ip, user_id, event_details):
        """Create a new security alert"""
        try:
            # Enhanced alert with threat intelligence
            enhanced_description = self._enhance_alert_description(description, source_ip, event_details)
            
            alert = SecurityAlert(
                alert_type=alert_type,
                severity=severity,
                title=title,
                description=enhanced_description,
                source_ip=source_ip,
                user_id=user_id,
                event_count=1,
                status='active'
            )
            
            db.session.add(alert)
            db.session.commit()
            
            # Send notifications for new alerts
            self._send_alert_notifications(alert, is_new=True)
            
            return True
            
        except Exception as e:
            db.session.rollback()
            if current_app:
                current_app.logger.error(f"Failed to create new alert: {str(e)}")
            return False

    def _update_grouped_alert(self, group_id, alert_type, severity, description):
        """Update an existing grouped alert"""
        try:
            alert = SecurityAlert.query.get(group_id)
            if alert:
                alert.event_count += 1
                alert.updated_at = datetime.utcnow()
                
                # Update description with new information
                if description not in alert.description:
                    alert.description += f"\\n\\n[Update {alert.event_count}]: {description}"
                
                db.session.commit()
                
                # Send notification only for significant updates (every 5th event or severity increase)
                should_notify = (alert.event_count % 5 == 0) or (severity == 'critical' and alert.severity != 'critical')
                
                if should_notify:
                    self._send_alert_notifications(alert, is_update=True)
                
                return True
            return False
            
        except Exception as e:
            db.session.rollback()
            if current_app:
                current_app.logger.error(f"Failed to update grouped alert: {str(e)}")
            return False

    def _create_summary_alert(self, summary_data):
        """Create a summary alert for grouped events"""
        try:
            if summary_data['type'] == 'ip_grouped':
                alert_type = f"grouped_ip_alerts_{summary_data['source_ip']}"
                title = f"Multiple Security Alerts from IP {summary_data['source_ip']}"
                description = f"Grouped {summary_data['alert_count']} alerts from IP {summary_data['source_ip']}. Alert types: {', '.join(summary_data['alert_types'])}"
                severity = 'high'
                source_ip = summary_data['source_ip']
                
            elif summary_data['type'] == 'pattern_grouped':
                alert_type = f"pattern_grouped_{hash(str(summary_data['pattern_types']))}"
                title = f"Attack Pattern Detected: {', '.join(summary_data['pattern_types'])}"
                description = f"Detected coordinated attack pattern with {summary_data['alert_count']} related alerts of types: {', '.join(summary_data['pattern_types'])}"
                severity = 'high'
                source_ip = None
                
            elif summary_data['type'] == 'severity_grouped':
                alert_type = f"severity_summary_{summary_data['severity']}"
                title = f"High Volume of {summary_data['severity'].title()} Alerts"
                description = f"Generated {summary_data['alert_count']} {summary_data['severity']} alerts in the last 15 minutes. This may indicate an ongoing attack."
                severity = 'critical' if summary_data['severity'] == 'critical' else 'high'
                source_ip = None
                
            else:
                return False
            
            alert = SecurityAlert(
                alert_type=alert_type,
                severity=severity,
                title=title,
                description=description,
                source_ip=source_ip,
                user_id=None,
                event_count=summary_data['alert_count'],
                status='active'
            )
            
            db.session.add(alert)
            db.session.commit()
            
            # Send high-priority notification for summary alerts
            self._send_alert_notifications(alert, is_summary=True)
            
            return True
            
        except Exception as e:
            db.session.rollback()
            if current_app:
                current_app.logger.error(f"Failed to create summary alert: {str(e)}")
            return False

    def _enhance_alert_description(self, description, source_ip, event_details):
        """Enhance alert description with threat intelligence and context"""
        enhanced_parts = [description]
        
        if source_ip:
            # Add IP intelligence
            ip_intel = self._get_ip_intelligence(source_ip)
            if ip_intel:
                enhanced_parts.append(f"\\n🔍 IP Intelligence: {ip_intel}")
            
            # Add recent activity summary
            recent_activity = self._get_recent_ip_activity(source_ip)
            if recent_activity:
                enhanced_parts.append(f"\\n📊 Recent Activity: {recent_activity}")
            
            # Add geographic info (if available)
            geo_info = self._get_geographic_info(source_ip)
            if geo_info:
                enhanced_parts.append(f"\\n🌍 Location: {geo_info}")
        
        # Add threat scoring
        if event_details:
            threat_score = self._calculate_threat_score(event_details)
            if threat_score > 0:
                enhanced_parts.append(f"\\n⚠️ Threat Score: {threat_score}/10")
        
        # Add recommended actions
        recommendations = self._get_recommended_actions(source_ip, event_details)
        if recommendations:
            enhanced_parts.append(f"\\n💡 Recommended Actions: {recommendations}")
        
        return "\\n".join(enhanced_parts)

    def _get_ip_intelligence(self, ip):
        """Get threat intelligence for IP address"""
        try:
            # Check if IP is already banned
            if SecurityBan.is_banned(ip):
                return "🚫 IP is currently banned"
            
            # Count recent security events
            recent_events = SecurityEvent.query.filter(
                SecurityEvent.source_ip == ip,
                SecurityEvent.timestamp >= datetime.utcnow() - timedelta(hours=24)
            ).count()
            
            if recent_events > 20:
                return f"🔴 High activity IP ({recent_events} events in 24h)"
            elif recent_events > 5:
                return f"🟡 Moderate activity IP ({recent_events} events in 24h)"
            else:
                return f"🟢 Low activity IP ({recent_events} events in 24h)"
                
        except Exception:
            return None

    def _get_recent_ip_activity(self, ip):
        """Get summary of recent IP activity"""
        try:
            # Get event types in last hour
            recent_events = SecurityEvent.query.filter(
                SecurityEvent.source_ip == ip,
                SecurityEvent.timestamp >= datetime.utcnow() - timedelta(hours=1)
            ).all()
            
            if not recent_events:
                return None
            
            event_types = Counter(e.event_type for e in recent_events)
            top_events = event_types.most_common(3)
            
            activity_summary = ", ".join([f"{event_type} ({count})" for event_type, count in top_events])
            return f"Last hour: {activity_summary}"
            
        except Exception:
            return None

    def _get_geographic_info(self, ip):
        """Get geographic information for IP (placeholder)"""
        # In production, integrate with GeoIP service
        return None

    def _calculate_threat_score(self, event_details):
        """Calculate threat score based on event details"""
        try:
            score = 0
            
            if isinstance(event_details, dict):
                # Score based on detection factors
                detection_factors = event_details.get('detection_factors', [])
                for factor in detection_factors:
                    if isinstance(factor, dict):
                        score += factor.get('score', 0)
                
                # Score based on severity keywords
                if 'critical' in str(event_details).lower():
                    score += 3
                elif 'high' in str(event_details).lower():
                    score += 2
                
                # Score based on specific threats
                threat_keywords = ['ddos', 'injection', 'xss', 'brute_force', 'botnet']
                for keyword in threat_keywords:
                    if keyword in str(event_details).lower():
                        score += 1
            
            return min(score, 10)  # Cap at 10
            
        except Exception:
            return 0

    def _get_recommended_actions(self, source_ip, event_details):
        """Get recommended actions based on alert context"""
        recommendations = []
        
        if source_ip:
            # Check if IP should be banned
            recent_severe_events = SecurityEvent.query.filter(
                SecurityEvent.source_ip == source_ip,
                SecurityEvent.severity.in_(['high', 'critical']),
                SecurityEvent.timestamp >= datetime.utcnow() - timedelta(hours=1)
            ).count()
            
            if recent_severe_events >= 5:
                recommendations.append("Consider IP ban")
            elif recent_severe_events >= 2:
                recommendations.append("Monitor IP closely")
        
        # Event-specific recommendations
        if isinstance(event_details, dict):
            event_type = event_details.get('event_type', '')
            
            if 'ddos' in event_type.lower():
                recommendations.extend(["Enable DDoS protection", "Check traffic patterns", "Consider rate limiting"])
            elif 'injection' in event_type.lower():
                recommendations.extend(["Review application logs", "Check for data breach", "Update WAF rules"])
            elif 'brute_force' in event_type.lower():
                recommendations.extend(["Reset affected passwords", "Enable 2FA", "Review access logs"])
        
        return " | ".join(recommendations) if recommendations else None

    def _send_alert_notifications(self, alert, is_new=False, is_update=False, is_summary=False):
        """Send alert notifications through configured channels"""
        try:
            # Determine notification priority
            if is_summary or alert.severity == 'critical':
                priority = 'high'
            elif alert.severity == 'high' or is_new:
                priority = 'medium'
            else:
                priority = 'low'
            
            # Check alert cooldown to prevent spam
            if not self._check_alert_cooldown(alert.alert_type, alert.source_ip):
                return False
            
            # Send to enabled channels
            success_count = 0
            
            if self.notification_channels['email']['enabled']:
                if self._send_email_notification(alert, priority):
                    success_count += 1
            
            if self.notification_channels['webhook']['enabled']:
                if self._send_webhook_notification(alert, priority):
                    success_count += 1
            
            if self.notification_channels['slack']['enabled']:
                if self._send_slack_notification(alert, priority):
                    success_count += 1
            
            if self.notification_channels['discord']['enabled']:
                if self._send_discord_notification(alert, priority):
                    success_count += 1
            
            # Update alert cooldown
            self._set_alert_cooldown(alert.alert_type, alert.source_ip)
            
            return success_count > 0
            
        except Exception as e:
            if current_app:
                current_app.logger.error(f"Failed to send alert notifications: {str(e)}")
            return False

    def _check_alert_cooldown(self, alert_type, source_ip):
        """Check if alert is in cooldown period"""
        cooldown_seconds = SecurityConfig.get_config('security_alert_cooldown', 300)  # 5 minutes default
        
        # Create cooldown key
        cooldown_key = f"alert_cooldown:{alert_type}:{source_ip or 'global'}"
        
        last_sent = cache.get(cooldown_key)
        if last_sent:
            if time.time() - last_sent < cooldown_seconds:
                return False  # Still in cooldown
        
        return True

    def _set_alert_cooldown(self, alert_type, source_ip):
        """Set alert cooldown"""
        cooldown_seconds = SecurityConfig.get_config('security_alert_cooldown', 300)
        cooldown_key = f"alert_cooldown:{alert_type}:{source_ip or 'global'}"
        cache.set(cooldown_key, time.time(), timeout=cooldown_seconds)

    def _send_email_notification(self, alert, priority):
        """Send email notification"""
        try:
            config = self.notification_channels['email']
            
            if not config['to_emails']:
                return False
            
            # Create email message
            msg = MIMEMultipart()
            msg['From'] = config['from_email']
            msg['To'] = ', '.join(config['to_emails'])
            msg['Subject'] = f"[CTFd Security] {alert.severity.upper()}: {alert.title}"
            
            # Create email body
            body = self._create_email_body(alert, priority)
            msg.attach(MIMEText(body, 'html'))
            
            # Send email
            server = smtplib.SMTP(config['smtp_server'], config['smtp_port'])
            if config['use_tls']:
                server.starttls()
            
            if config['smtp_username'] and config['smtp_password']:
                server.login(config['smtp_username'], config['smtp_password'])
            
            server.sendmail(config['from_email'], config['to_emails'], msg.as_string())
            server.quit()
            
            return True
            
        except Exception as e:
            if current_app:
                current_app.logger.error(f"Failed to send email notification: {str(e)}")
            return False

    def _send_webhook_notification(self, alert, priority):
        """Send webhook notification"""
        try:
            config = self.notification_channels['webhook']
            
            if not config['webhook_url']:
                return False
            
            # Create payload
            payload = {
                'alert_id': alert.id,
                'alert_type': alert.alert_type,
                'severity': alert.severity,
                'priority': priority,
                'title': alert.title,
                'description': alert.description,
                'source_ip': alert.source_ip,
                'event_count': alert.event_count,
                'status': alert.status,
                'created_at': alert.created_at.isoformat(),
                'updated_at': alert.updated_at.isoformat() if alert.updated_at else None,
                'platform': 'CTFd',
                'timestamp': datetime.utcnow().isoformat()
            }
            
            # Add signature if secret is configured
            headers = {'Content-Type': 'application/json'}
            headers.update(config.get('custom_headers', {}))
            
            if config['webhook_secret']:
                signature = self._generate_webhook_signature(json.dumps(payload), config['webhook_secret'])
                headers['X-Signature'] = signature
            
            # Send webhook
            response = requests.post(
                config['webhook_url'],
                json=payload,
                headers=headers,
                timeout=10
            )
            
            return response.status_code < 400
            
        except Exception as e:
            if current_app:
                current_app.logger.error(f"Failed to send webhook notification: {str(e)}")
            return False

    def _send_slack_notification(self, alert, priority):
        """Send Slack notification"""
        try:
            config = self.notification_channels['slack']
            
            if not config['webhook_url']:
                return False
            
            # Create Slack message
            color = {
                'critical': '#FF0000',  # Red
                'high': '#FF6600',      # Orange
                'medium': '#FFCC00',    # Yellow
                'low': '#00FF00'        # Green
            }.get(alert.severity, '#808080')
            
            payload = {
                'channel': config['channel'],
                'username': config['username'],
                'attachments': [{
                    'color': color,
                    'title': f"{alert.severity.upper()}: {alert.title}",
                    'text': alert.description[:500] + ('...' if len(alert.description) > 500 else ''),
                    'fields': [
                        {
                            'title': 'Source IP',
                            'value': alert.source_ip or 'N/A',
                            'short': True
                        },
                        {
                            'title': 'Event Count',
                            'value': str(alert.event_count),
                            'short': True
                        },
                        {
                            'title': 'Alert Type',
                            'value': alert.alert_type,
                            'short': True
                        },
                        {
                            'title': 'Time',
                            'value': alert.created_at.strftime('%Y-%m-%d %H:%M:%S UTC'),
                            'short': True
                        }
                    ],
                    'footer': 'CTFd Security Monitor',
                    'ts': int(alert.created_at.timestamp())
                }]
            }
            
            response = requests.post(config['webhook_url'], json=payload, timeout=10)
            return response.status_code < 400
            
        except Exception as e:
            if current_app:
                current_app.logger.error(f"Failed to send Slack notification: {str(e)}")
            return False

    def _send_discord_notification(self, alert, priority):
        """Send Discord notification"""
        try:
            config = self.notification_channels['discord']
            
            if not config['webhook_url']:
                return False
            
            # Create Discord embed
            color = {
                'critical': 16711680,  # Red
                'high': 16744448,      # Orange
                'medium': 16776960,    # Yellow
                'low': 65280          # Green
            }.get(alert.severity, 8421504)  # Gray
            
            embed = {
                'title': f"{alert.severity.upper()}: {alert.title}",
                'description': alert.description[:2000],  # Discord limit
                'color': color,
                'fields': [
                    {
                        'name': 'Source IP',
                        'value': alert.source_ip or 'N/A',
                        'inline': True
                    },
                    {
                        'name': 'Event Count',
                        'value': str(alert.event_count),
                        'inline': True
                    },
                    {
                        'name': 'Alert Type',
                        'value': alert.alert_type,
                        'inline': True
                    }
                ],
                'footer': {
                    'text': 'CTFd Security Monitor'
                },
                'timestamp': alert.created_at.isoformat()
            }
            
            payload = {
                'username': config['username'],
                'embeds': [embed]
            }
            
            response = requests.post(config['webhook_url'], json=payload, timeout=10)
            return response.status_code < 400
            
        except Exception as e:
            if current_app:
                current_app.logger.error(f"Failed to send Discord notification: {str(e)}")
            return False

    def _create_email_body(self, alert, priority):
        """Create HTML email body"""
        severity_colors = {
            'critical': '#FF0000',
            'high': '#FF6600',
            'medium': '#FFCC00',
            'low': '#00FF00'
        }
        
        color = severity_colors.get(alert.severity, '#808080')
        
        html = f"""
        <html>
        <body style="font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5;">
            <div style="max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <div style="background-color: {color}; color: white; padding: 20px; text-align: center;">
                    <h1 style="margin: 0; font-size: 24px;">🛡️ CTFd Security Alert</h1>
                    <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">{alert.severity.upper()} Priority</p>
                </div>
                
                <div style="padding: 30px;">
                    <h2 style="color: #333; margin-top: 0;">{alert.title}</h2>
                    
                    <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                        <p style="margin: 0; white-space: pre-line;">{alert.description}</p>
                    </div>
                    
                    <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
                        <tr>
                            <td style="padding: 10px; border-bottom: 1px solid #eee; font-weight: bold; width: 30%;">Alert Type:</td>
                            <td style="padding: 10px; border-bottom: 1px solid #eee;">{alert.alert_type}</td>
                        </tr>
                        <tr>
                            <td style="padding: 10px; border-bottom: 1px solid #eee; font-weight: bold;">Source IP:</td>
                            <td style="padding: 10px; border-bottom: 1px solid #eee;">{alert.source_ip or 'N/A'}</td>
                        </tr>
                        <tr>
                            <td style="padding: 10px; border-bottom: 1px solid #eee; font-weight: bold;">Event Count:</td>
                            <td style="padding: 10px; border-bottom: 1px solid #eee;">{alert.event_count}</td>
                        </tr>
                        <tr>
                            <td style="padding: 10px; border-bottom: 1px solid #eee; font-weight: bold;">Created:</td>
                            <td style="padding: 10px; border-bottom: 1px solid #eee;">{alert.created_at.strftime('%Y-%m-%d %H:%M:%S UTC')}</td>
                        </tr>
                    </table>
                    
                    <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; text-align: center; color: #666; font-size: 14px;">
                        <p>This alert was generated by CTFd Security Monitor</p>
                        <p>Alert ID: {alert.id} | Priority: {priority.upper()}</p>
                    </div>
                </div>
            </div>
        </body>
        </html>
        """
        
        return html

    def _generate_webhook_signature(self, payload, secret):
        """Generate webhook signature for verification"""
        import hmac
        signature = hmac.new(
            secret.encode(),
            payload.encode(),
            hashlib.sha256
        ).hexdigest()
        return f"sha256={signature}"

    def test_alert_channels(self):
        """Test all configured alert channels"""
        results = {}
        
        # Create a test alert
        test_alert = SecurityAlert(
            id=999999,
            alert_type='test_alert',
            severity='medium',
            title='Test Alert - Please Ignore',
            description='This is a test alert to verify notification channels are working correctly.',
            source_ip='127.0.0.1',
            user_id=None,
            event_count=1,
            status='active',
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        # Test each channel
        if self.notification_channels['email']['enabled']:
            results['email'] = self._send_email_notification(test_alert, 'low')
        
        if self.notification_channels['webhook']['enabled']:
            results['webhook'] = self._send_webhook_notification(test_alert, 'low')
        
        if self.notification_channels['slack']['enabled']:
            results['slack'] = self._send_slack_notification(test_alert, 'low')
        
        if self.notification_channels['discord']['enabled']:
            results['discord'] = self._send_discord_notification(test_alert, 'low')
        
        return {
            'success': any(results.values()),
            'results': results,
            'message': 'Test notifications sent to enabled channels'
        }

    def get_alert_statistics(self):
        """Get alert statistics and metrics"""
        try:
            now = datetime.utcnow()
            last_24h = now - timedelta(hours=24)
            last_7d = now - timedelta(days=7)
            
            stats = {
                'total_alerts_24h': SecurityAlert.query.filter(
                    SecurityAlert.created_at >= last_24h
                ).count(),
                
                'total_alerts_7d': SecurityAlert.query.filter(
                    SecurityAlert.created_at >= last_7d
                ).count(),
                
                'active_alerts': SecurityAlert.query.filter_by(status='active').count(),
                
                'critical_alerts_24h': SecurityAlert.query.filter(
                    SecurityAlert.created_at >= last_24h,
                    SecurityAlert.severity == 'critical'
                ).count(),
                
                'alert_resolution_rate': self._calculate_resolution_rate(),
                
                'top_alert_types': self._get_top_alert_types(),
                
                'alert_trends': self._get_alert_trends()
            }
            
            return stats
            
        except Exception as e:
            if current_app:
                current_app.logger.error(f"Failed to get alert statistics: {str(e)}")
            return {}

    def _calculate_resolution_rate(self):
        """Calculate alert resolution rate"""
        try:
            last_7d = datetime.utcnow() - timedelta(days=7)
            
            total_alerts = SecurityAlert.query.filter(
                SecurityAlert.created_at >= last_7d
            ).count()
            
            resolved_alerts = SecurityAlert.query.filter(
                SecurityAlert.created_at >= last_7d,
                SecurityAlert.status.in_(['resolved', 'acknowledged'])
            ).count()
            
            if total_alerts == 0:
                return 0
            
            return round((resolved_alerts / total_alerts) * 100, 2)
            
        except Exception:
            return 0

    def _get_top_alert_types(self):
        """Get top alert types in the last 24 hours"""
        try:
            last_24h = datetime.utcnow() - timedelta(hours=24)
            
            alert_types = db.session.query(
                SecurityAlert.alert_type,
                db.func.count(SecurityAlert.id).label('count')
            ).filter(
                SecurityAlert.created_at >= last_24h
            ).group_by(SecurityAlert.alert_type).order_by(
                db.func.count(SecurityAlert.id).desc()
            ).limit(10).all()
            
            return [{'type': alert_type, 'count': count} for alert_type, count in alert_types]
            
        except Exception:
            return []

    def _get_alert_trends(self):
        """Get alert trends over the last 7 days"""
        try:
            trends = []
            
            for i in range(7):
                day_start = datetime.utcnow() - timedelta(days=i+1)
                day_end = day_start + timedelta(days=1)
                
                day_alerts = SecurityAlert.query.filter(
                    SecurityAlert.created_at >= day_start,
                    SecurityAlert.created_at < day_end
                ).count()
                
                trends.append({
                    'date': day_start.strftime('%Y-%m-%d'),
                    'count': day_alerts
                })
            
            return list(reversed(trends))  # Oldest to newest
            
        except Exception:
            return []
