# CTFd_with_CTFd-whale

[ZH-README](https://github.com/Err0rCM/CTFd_with_CTFd-whale/blob/main/ZH-README.md)

This repository is forked from [CTFD3.2.1](https://github.com/CTFd/CTFd).I add something from [CTFD-whale@frankli0324](https://github.com/frankli0324/CTFd-Whale).It was origined from [glzjin](https://github.com/glzjin/CTFd-Whale)

Ctfd version 3.2.1 and FRP version 0.29.0 are used in this repository.

This is just a example for a plugin for CTFd which allow your users to launch a standalone instance for challenges.

I am still a student ,so i can't provide professional help.If you have any questions, please ask them in issue. I can try my best to answer them. Thank you again!

Can refer to my article https://err0r.top/article/CTFD/, my English ability is limited

`RUN docker-compose up -d`