/* Security Monitor Plugin Styles */

.security-dashboard {
    background: #f8f9fa;
    min-height: 100vh;
}

.security-card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.2s ease-in-out;
}

.security-card:hover {
    transform: translateY(-2px);
}

.security-metric-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
}

.security-alert-critical {
    border-left: 4px solid #dc3545;
    background-color: #f8d7da;
}

.security-alert-high {
    border-left: 4px solid #fd7e14;
    background-color: #fff3cd;
}

.security-alert-medium {
    border-left: 4px solid #17a2b8;
    background-color: #d1ecf1;
}

.security-alert-low {
    border-left: 4px solid #28a745;
    background-color: #d4edda;
}

.security-event-row {
    transition: background-color 0.2s ease;
}

.security-event-row:hover {
    background-color: #f8f9fa;
}

.security-status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
}

.security-ip-code {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    background-color: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.875rem;
}

.security-chart-container {
    position: relative;
    height: 300px;
    margin: 20px 0;
}

.security-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.security-threat-list {
    max-height: 400px;
    overflow-y: auto;
}

.security-config-section {
    margin-bottom: 30px;
}

.security-config-section .card-header {
    background-color: #495057;
    color: white;
    font-weight: 600;
}

.security-toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.security-toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.security-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.security-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .security-slider {
    background-color: #2196F3;
}

input:checked + .security-slider:before {
    transform: translateX(26px);
}

.security-real-time-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    background-color: #28a745;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
    }
    
    70% {
        transform: scale(1);
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }
    
    100% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

.security-loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.security-severity-critical {
    color: #dc3545;
    font-weight: bold;
}

.security-severity-high {
    color: #fd7e14;
    font-weight: bold;
}

.security-severity-medium {
    color: #17a2b8;
    font-weight: bold;
}

.security-severity-low {
    color: #28a745;
    font-weight: bold;
}

.security-table-responsive {
    border-radius: 10px;
    overflow: hidden;
}

.security-table th {
    background-color: #495057;
    color: white;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.5px;
}

.security-table td {
    border-color: #dee2e6;
    vertical-align: middle;
}

.security-action-buttons .btn {
    margin-right: 5px;
    margin-bottom: 5px;
}

.security-filter-form {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.security-pagination {
    margin-top: 20px;
}

.security-modal .modal-content {
    border-radius: 10px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.security-modal .modal-header {
    background-color: #495057;
    color: white;
    border-radius: 10px 10px 0 0;
}

.security-json-viewer {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 15px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    max-height: 400px;
    overflow-y: auto;
}

.security-dashboard-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px 0;
    margin-bottom: 30px;
}

.security-dashboard-header h1 {
    font-size: 2.5rem;
    font-weight: 300;
    margin-bottom: 10px;
}

.security-dashboard-header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.security-quick-actions {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
}

.security-quick-actions .btn {
    margin-bottom: 10px;
    border-radius: 50px;
    padding: 10px 20px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

@media (max-width: 768px) {
    .security-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .security-action-buttons {
        display: flex;
        flex-direction: column;
    }
    
    .security-action-buttons .btn {
        margin-bottom: 10px;
        margin-right: 0;
    }
    
    .security-quick-actions {
        position: relative;
        bottom: auto;
        right: auto;
        margin-top: 20px;
    }
}
