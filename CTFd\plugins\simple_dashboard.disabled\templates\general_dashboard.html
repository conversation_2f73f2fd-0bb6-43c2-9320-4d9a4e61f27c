{% extends "admin/base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">📊 General CTFd Dashboard</h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-primary btn-sm" onclick="refreshStats()">
                            <i class="fas fa-sync"></i> Refresh
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Overview Stats -->
                    <div class="row">
                        <div class="col-lg-3 col-6">
                            <div class="small-box bg-info">
                                <div class="inner">
                                    <h3 id="total-users">{{ stats.total_users }}</h3>
                                    <p>Total Users</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-users"></i>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-3 col-6">
                            <div class="small-box bg-success">
                                <div class="inner">
                                    <h3 id="total-challenges">{{ stats.total_challenges }}</h3>
                                    <p>Total Challenges</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-flag"></i>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-3 col-6">
                            <div class="small-box bg-warning">
                                <div class="inner">
                                    <h3 id="total-submissions">{{ stats.total_submissions }}</h3>
                                    <p>Total Submissions</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-paper-plane"></i>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-3 col-6">
                            <div class="small-box bg-danger">
                                <div class="inner">
                                    <h3 id="success-rate">{{ stats.success_rate }}%</h3>
                                    <p>Success Rate</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 24h Activity -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h4>Recent Activity (24 hours)</h4>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="info-box">
                                                <span class="info-box-icon bg-info"><i class="fas fa-user-plus"></i></span>
                                                <div class="info-box-content">
                                                    <span class="info-box-text">New Users</span>
                                                    <span class="info-box-number">{{ stats.new_users_24h }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="info-box">
                                                <span class="info-box-icon bg-success"><i class="fas fa-paper-plane"></i></span>
                                                <div class="info-box-content">
                                                    <span class="info-box-text">Submissions</span>
                                                    <span class="info-box-number">{{ stats.submissions_24h }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="info-box">
                                                <span class="info-box-icon bg-warning"><i class="fas fa-trophy"></i></span>
                                                <div class="info-box-content">
                                                    <span class="info-box-text">Solves</span>
                                                    <span class="info-box-number">{{ stats.solves_24h }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Top Users and Challenges -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h4>Most Active Users</h4>
                                </div>
                                <div class="card-body">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>User</th>
                                                <th>Submissions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for user in stats.top_users %}
                                            <tr>
                                                <td>{{ user.name }}</td>
                                                <td><span class="badge badge-primary">{{ user.submission_count }}</span></td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h4>Most Attempted Challenges</h4>
                                </div>
                                <div class="card-body">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Challenge</th>
                                                <th>Attempts</th>
                                                <th>Solves</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for challenge in stats.challenge_stats %}
                                            <tr>
                                                <td>{{ challenge.name }}</td>
                                                <td><span class="badge badge-warning">{{ challenge.attempt_count }}</span></td>
                                                <td><span class="badge badge-success">{{ challenge.solve_count }}</span></td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recent Activity Timeline -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h4>Recent Submissions</h4>
                                </div>
                                <div class="card-body">
                                    <div class="timeline">
                                        {% for submission in stats.recent_submissions[:10] %}
                                        <div class="time-label">
                                            <span class="bg-info">{{ submission.date.strftime('%H:%M') if submission.date else 'Unknown' }}</span>
                                        </div>
                                        <div>
                                            <i class="fas fa-paper-plane bg-blue"></i>
                                            <div class="timeline-item">
                                                <span class="time"><i class="fas fa-clock"></i> {{ submission.date.strftime('%Y-%m-%d %H:%M:%S') if submission.date else 'Unknown time' }}</span>
                                                <h3 class="timeline-header">
                                                    User: {{ submission.user.name if submission.user else 'Unknown' }}
                                                </h3>
                                                <div class="timeline-body">
                                                    Challenge: {{ submission.challenge.name if submission.challenge else 'Unknown' }}
                                                    {% if submission.type == 'correct' %}
                                                    <span class="badge badge-success ml-2">Correct</span>
                                                    {% else %}
                                                    <span class="badge badge-danger ml-2">Incorrect</span>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                        {% else %}
                                        <p class="text-muted">No recent submissions available.</p>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function refreshStats() {
    const btn = document.querySelector('[onclick="refreshStats()"]');
    btn.innerHTML = '<i class="fas fa-spin fa-spinner"></i> Refreshing...';
    btn.disabled = true;
    
    fetch('/plugins/simple_dashboard/api/general-stats')
        .then(response => response.json())
        .then(data => {
            // Update the stats
            document.getElementById('total-users').textContent = data.total_users;
            document.getElementById('total-submissions').textContent = data.total_submissions;
            
            // Show success message
            const alert = document.createElement('div');
            alert.className = 'alert alert-success alert-dismissible fade show';
            alert.innerHTML = `
                <strong>Success!</strong> Dashboard refreshed at ${new Date(data.timestamp).toLocaleTimeString()}
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
            `;
            document.querySelector('.card-body').insertBefore(alert, document.querySelector('.row'));
            
            // Auto-dismiss after 3 seconds
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 3000);
        })
        .catch(error => {
            console.error('Error refreshing stats:', error);
            alert('Failed to refresh statistics');
        })
        .finally(() => {
            btn.innerHTML = '<i class="fas fa-sync"></i> Refresh';
            btn.disabled = false;
        });
}

// Auto-refresh every 5 minutes
setInterval(refreshStats, 300000);
</script>

<style>
.timeline {
    position: relative;
    margin: 0 0 30px 0;
    padding: 0;
    list-style: none;
}

.timeline:before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    width: 4px;
    background: #ddd;
    left: 31px;
    margin: 0;
}

.timeline > div {
    margin-bottom: 15px;
    position: relative;
}

.timeline > div > .timeline-item {
    background: #f4f4f4;
    border-radius: 3px;
    padding: 10px;
    margin-left: 60px;
    position: relative;
}

.timeline > div > i {
    position: absolute;
    left: 18px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    text-align: center;
    line-height: 30px;
    font-size: 15px;
    color: white;
}

.timeline .time-label > span {
    font-weight: 600;
    color: #fff;
    font-size: 12px;
    padding: 5px;
    border-radius: 4px;
}

.bg-blue { background-color: #007bff !important; }
</style>
{% endblock %}
