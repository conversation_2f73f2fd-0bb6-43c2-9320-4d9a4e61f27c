"""Merge all migration heads

Revision ID: final_heads_merge
Revises: 75e8ab9a0014, 080d29b15cd3
Create Date: 2025-05-28 19:40:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'final_heads_merge'
down_revision = ('75e8ab9a0014', '080d29b15cd3')
branch_labels = None
depends_on = None


def upgrade():
    """Merge multiple heads - no schema changes needed"""
    pass


def downgrade():
    """Cannot downgrade a merge revision"""
    pass
