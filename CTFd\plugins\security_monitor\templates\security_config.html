{% extends "admin/base.html" %}

{% block content %}
<div class="jumbotron">
    <div class="container">
        <h1>🔧 Security Monitor Configuration</h1>
        <p>Configure security monitoring settings and alert channels</p>
    </div>
</div>

<div class="container">
    <form id="securityConfigForm">
        <!-- Rate Limiting Configuration -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>Rate Limiting</h5>
            </div>
            <div class="card-body">
                <div class="form-group">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="rate_limit_enabled" name="security_rate_limit_enabled" {% if config.security_rate_limit_enabled %}checked{% endif %}>
                        <label class="form-check-label" for="rate_limit_enabled">
                            Enable Rate Limiting
                        </label>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="rate_limit_requests">Requests per Window</label>
                            <input type="number" class="form-control" id="rate_limit_requests" name="security_rate_limit_requests" value="{{ config.security_rate_limit_requests or 100 }}">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="rate_limit_window">Window Size (seconds)</label>
                            <input type="number" class="form-control" id="rate_limit_window" name="security_rate_limit_window" value="{{ config.security_rate_limit_window or 60 }}">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- DDoS Detection Configuration -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>DDoS Detection</h5>
            </div>
            <div class="card-body">
                <div class="form-group">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="ddos_detection_enabled" name="security_ddos_detection_enabled" {% if config.security_ddos_detection_enabled %}checked{% endif %}>
                        <label class="form-check-label" for="ddos_detection_enabled">
                            Enable DDoS Detection
                        </label>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="ddos_threshold">Request Threshold</label>
                            <input type="number" class="form-control" id="ddos_threshold" name="security_ddos_threshold" value="{{ config.security_ddos_threshold or 1000 }}">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="ddos_window">Detection Window (seconds)</label>
                            <input type="number" class="form-control" id="ddos_window" name="security_ddos_window" value="{{ config.security_ddos_window or 60 }}">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Suspicious Activity Detection -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>Suspicious Activity Detection</h5>
            </div>
            <div class="card-body">
                <div class="form-group">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="suspicious_activity_enabled" name="security_suspicious_activity_enabled" {% if config.security_suspicious_activity_enabled %}checked{% endif %}>
                        <label class="form-check-label" for="suspicious_activity_enabled">
                            Enable Suspicious Activity Detection
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Auto-Ban Configuration -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>Automatic Banning</h5>
            </div>
            <div class="card-body">
                <div class="form-group">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="auto_ban_enabled" name="security_auto_ban_enabled" {% if config.security_auto_ban_enabled %}checked{% endif %}>
                        <label class="form-check-label" for="auto_ban_enabled">
                            Enable Automatic IP Banning
                        </label>
                    </div>
                </div>
                <div class="form-group">
                    <label for="auto_ban_threshold">Security Events Threshold</label>
                    <input type="number" class="form-control" id="auto_ban_threshold" name="security_auto_ban_threshold" value="{{ config.security_auto_ban_threshold or 10 }}">
                    <small class="form-text text-muted">Number of security events within 1 hour before auto-banning</small>
                </div>
            </div>
        </div>

        <!-- Email Alerts Configuration -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>Email Alerts</h5>
            </div>
            <div class="card-body">
                <div class="form-group">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="email_alerts_enabled" name="security_email_alerts_enabled" {% if config.security_email_alerts_enabled %}checked{% endif %}>
                        <label class="form-check-label" for="email_alerts_enabled">
                            Enable Email Alerts
                        </label>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="smtp_server">SMTP Server</label>
                            <input type="text" class="form-control" id="smtp_server" name="security_smtp_server" value="{{ config.security_smtp_server or '' }}">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="smtp_port">SMTP Port</label>
                            <input type="number" class="form-control" id="smtp_port" name="security_smtp_port" value="{{ config.security_smtp_port or 587 }}">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="smtp_username">SMTP Username</label>
                            <input type="text" class="form-control" id="smtp_username" name="security_smtp_username" value="{{ config.security_smtp_username or '' }}">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="smtp_password">SMTP Password</label>
                            <input type="password" class="form-control" id="smtp_password" name="security_smtp_password" value="{{ config.security_smtp_password or '' }}">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="alert_from_email">From Email</label>
                            <input type="email" class="form-control" id="alert_from_email" name="security_alert_from_email" value="{{ config.security_alert_from_email or '' }}">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="alert_to_emails">To Emails (comma-separated)</label>
                            <input type="text" class="form-control" id="alert_to_emails" name="security_alert_to_emails" value="{{ config.security_alert_to_emails | join(',') if config.security_alert_to_emails else '' }}">
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="smtp_use_tls" name="security_smtp_use_tls" {% if config.security_smtp_use_tls %}checked{% endif %}>
                        <label class="form-check-label" for="smtp_use_tls">
                            Use TLS
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Webhook Alerts Configuration -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>Webhook Alerts</h5>
            </div>
            <div class="card-body">
                <div class="form-group">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="webhook_alerts_enabled" name="security_webhook_alerts_enabled" {% if config.security_webhook_alerts_enabled %}checked{% endif %}>
                        <label class="form-check-label" for="webhook_alerts_enabled">
                            Enable Webhook Alerts
                        </label>
                    </div>
                </div>
                <div class="form-group">
                    <label for="webhook_url">Webhook URL</label>
                    <input type="url" class="form-control" id="webhook_url" name="security_webhook_url" value="{{ config.security_webhook_url or '' }}">
                </div>
                <div class="form-group">
                    <label for="webhook_secret">Webhook Secret (optional)</label>
                    <input type="password" class="form-control" id="webhook_secret" name="security_webhook_secret" value="{{ config.security_webhook_secret or '' }}">
                </div>
            </div>
        </div>

        <!-- Slack Alerts Configuration -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>Slack Alerts</h5>
            </div>
            <div class="card-body">
                <div class="form-group">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="slack_alerts_enabled" name="security_slack_alerts_enabled" {% if config.security_slack_alerts_enabled %}checked{% endif %}>
                        <label class="form-check-label" for="slack_alerts_enabled">
                            Enable Slack Alerts
                        </label>
                    </div>
                </div>
                <div class="form-group">
                    <label for="slack_webhook_url">Slack Webhook URL</label>
                    <input type="url" class="form-control" id="slack_webhook_url" name="security_slack_webhook_url" value="{{ config.security_slack_webhook_url or '' }}">
                </div>
                <div class="form-group">
                    <label for="slack_channel">Slack Channel</label>
                    <input type="text" class="form-control" id="slack_channel" name="security_slack_channel" value="{{ config.security_slack_channel or '#security' }}">
                </div>
            </div>
        </div>

        <!-- General Settings -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>General Settings</h5>
            </div>
            <div class="card-body">
                <div class="form-group">
                    <label for="log_retention_days">Log Retention (days)</label>
                    <input type="number" class="form-control" id="log_retention_days" name="security_log_retention_days" value="{{ config.security_log_retention_days or 30 }}">
                    <small class="form-text text-muted">Number of days to keep security logs</small>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="card">
            <div class="card-body">
                <button type="submit" class="btn btn-primary">Save Configuration</button>
                <button type="button" class="btn btn-secondary" onclick="testAlerts()">Test Alerts</button>
                <a href="/plugins/security_monitor/admin/" class="btn btn-outline-secondary">Back to Dashboard</a>
            </div>
        </div>
    </form>
</div>

<script>
document.getElementById('securityConfigForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const data = {};

    // Convert form data to object
    for (let [key, value] of formData.entries()) {
        if (key.includes('emails')) {
            data[key] = value.split(',').map(email => email.trim()).filter(email => email);
        } else if (this.querySelector(`[name="${key}"]`).type === 'checkbox') {
            data[key] = this.querySelector(`[name="${key}"]`).checked;
        } else if (this.querySelector(`[name="${key}"]`).type === 'number') {
            data[key] = parseInt(value) || 0;
        } else {
            data[key] = value;
        }
    }

    fetch('/plugins/security_monitor/admin/config', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'CSRF-Token': "{{ nonce }}"
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Configuration saved successfully!');
        } else {
            alert('Error saving configuration: ' + data.message);
        }
    })
    .catch(error => {
        alert('Error saving configuration: ' + error.message);
    });
});

function testAlerts() {
    fetch('/api/v1/plugins/security_monitor/test-alerts', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'CSRF-Token': "{{ nonce }}"
        }
    })
    .then(response => response.json())
    .then(data => {
        let message = 'Alert test results:\n';
        for (let [channel, result] of Object.entries(data)) {
            message += `${channel}: ${result ? 'Success' : 'Failed'}\n`;
        }
        alert(message);
    })
    .catch(error => {
        alert('Error testing alerts: ' + error.message);
    });
}
</script>
{% endblock %}
