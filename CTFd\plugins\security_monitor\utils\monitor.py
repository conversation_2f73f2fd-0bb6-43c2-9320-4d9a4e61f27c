import json
import subprocess
from datetime import datetime, timedelta
from collections import defaultdict
from CTFd.models import db, Users, Submissions, Fails
from ..models import SecurityEvent, SecurityAlert, SecurityMetrics, SecurityBan

class SecurityMonitor:
    """Security monitoring and statistics engine"""

    def __init__(self):
        pass

    def get_security_stats(self):
        """Get comprehensive security statistics"""
        now = datetime.utcnow()
        last_24h = now - timedelta(hours=24)
        last_7d = now - timedelta(days=7)
        last_30d = now - timedelta(days=30)

        stats = {
            'overview': self._get_overview_stats(last_24h, last_7d, last_30d),
            'events': self._get_event_stats(last_24h, last_7d),
            'alerts': self._get_alert_stats(last_24h, last_7d),
            'top_threats': self._get_top_threats(last_24h),
            'metrics': self._get_security_metrics(last_24h),
            'bans': self._get_ban_stats(),
            'trends': self._get_security_trends(last_7d),
            'container_stats': self._get_container_stats(),
            'flag_attempts': self._get_flag_attempt_stats(last_24h, last_7d)
        }

        return stats

    def _get_overview_stats(self, last_24h, last_7d, last_30d):
        """Get overview statistics"""
        return {
            'total_events_24h': SecurityEvent.query.filter(
                SecurityEvent.timestamp >= last_24h
            ).count(),
            'total_events_7d': SecurityEvent.query.filter(
                SecurityEvent.timestamp >= last_7d
            ).count(),
            'total_events_30d': SecurityEvent.query.filter(
                SecurityEvent.timestamp >= last_30d
            ).count(),
            'active_alerts': SecurityAlert.query.filter_by(status='active').count(),
            'active_bans': SecurityBan.query.filter_by(is_active=True).count(),
            'critical_events_24h': SecurityEvent.query.filter(
                SecurityEvent.timestamp >= last_24h,
                SecurityEvent.severity == 'critical'
            ).count(),
            'unique_ips_24h': db.session.query(SecurityEvent.source_ip).filter(
                SecurityEvent.timestamp >= last_24h
            ).distinct().count()
        }

    def _get_event_stats(self, last_24h, last_7d):
        """Get event statistics by type and severity"""
        # Events by type (last 24h)
        events_by_type = db.session.query(
            SecurityEvent.event_type,
            db.func.count(SecurityEvent.id).label('count')
        ).filter(
            SecurityEvent.timestamp >= last_24h
        ).group_by(SecurityEvent.event_type).all()

        # Events by severity (last 24h)
        events_by_severity = db.session.query(
            SecurityEvent.severity,
            db.func.count(SecurityEvent.id).label('count')
        ).filter(
            SecurityEvent.timestamp >= last_24h
        ).group_by(SecurityEvent.severity).all()

        # Hourly event distribution (last 24h)
        hourly_events = []
        for i in range(24):
            hour_start = last_24h + timedelta(hours=i)
            hour_end = hour_start + timedelta(hours=1)
            count = SecurityEvent.query.filter(
                SecurityEvent.timestamp >= hour_start,
                SecurityEvent.timestamp < hour_end
            ).count()
            hourly_events.append({
                'hour': hour_start.strftime('%H:00'),
                'count': count
            })

        return {
            'by_type': [{'type': t, 'count': c} for t, c in events_by_type],
            'by_severity': [{'severity': s, 'count': c} for s, c in events_by_severity],
            'hourly_distribution': hourly_events
        }

    def _get_alert_stats(self, last_24h, last_7d):
        """Get alert statistics"""
        alerts_by_type = db.session.query(
            SecurityAlert.alert_type,
            db.func.count(SecurityAlert.id).label('count')
        ).filter(
            SecurityAlert.created_at >= last_24h
        ).group_by(SecurityAlert.alert_type).all()

        alerts_by_severity = db.session.query(
            SecurityAlert.severity,
            db.func.count(SecurityAlert.id).label('count')
        ).filter(
            SecurityAlert.created_at >= last_24h
        ).group_by(SecurityAlert.severity).all()

        return {
            'by_type': [{'type': t, 'count': c} for t, c in alerts_by_type],
            'by_severity': [{'severity': s, 'count': c} for s, c in alerts_by_severity],
            'resolution_rate': self._calculate_alert_resolution_rate(last_7d)
        }

    def _get_top_threats(self, last_24h):
        """Get top threat sources"""
        # Top IPs by event count
        top_ips = db.session.query(
            SecurityEvent.source_ip,
            db.func.count(SecurityEvent.id).label('event_count'),
            db.func.max(SecurityEvent.severity).label('max_severity')
        ).filter(
            SecurityEvent.timestamp >= last_24h
        ).group_by(SecurityEvent.source_ip).order_by(
            db.func.count(SecurityEvent.id).desc()
        ).limit(10).all()

        # Top event types
        top_event_types = db.session.query(
            SecurityEvent.event_type,
            db.func.count(SecurityEvent.id).label('count'),
            db.func.count(db.distinct(SecurityEvent.source_ip)).label('unique_ips')
        ).filter(
            SecurityEvent.timestamp >= last_24h
        ).group_by(SecurityEvent.event_type).order_by(
            db.func.count(SecurityEvent.id).desc()
        ).limit(10).all()

        return {
            'top_ips': [
                {
                    'ip': ip,
                    'event_count': count,
                    'max_severity': severity,
                    'is_banned': SecurityBan.is_banned(ip)
                }
                for ip, count, severity in top_ips
            ],
            'top_event_types': [
                {
                    'event_type': event_type,
                    'count': count,
                    'unique_ips': unique_ips
                }
                for event_type, count, unique_ips in top_event_types
            ]
        }

    def _get_security_metrics(self, last_24h):
        """Get security metrics"""
        metrics = {}

        # Rate limit metrics
        rate_limit_metrics = SecurityMetrics.query.filter(
            SecurityMetrics.metric_type == 'rate_limit_exceeded',
            SecurityMetrics.timestamp >= last_24h
        ).all()

        if rate_limit_metrics:
            metrics['rate_limits'] = {
                'total_violations': len(rate_limit_metrics),
                'avg_requests': sum(m.metric_value for m in rate_limit_metrics) / len(rate_limit_metrics),
                'unique_ips': len(set(m.source_ip for m in rate_limit_metrics if m.source_ip))
            }

        # DDoS metrics
        ddos_metrics = SecurityMetrics.query.filter(
            SecurityMetrics.metric_type == 'ddos_detected',
            SecurityMetrics.timestamp >= last_24h
        ).all()

        if ddos_metrics:
            metrics['ddos'] = {
                'total_attacks': len(ddos_metrics),
                'max_requests': max(m.metric_value for m in ddos_metrics),
                'unique_ips': len(set(m.source_ip for m in ddos_metrics if m.source_ip))
            }

        # Suspicious activity metrics
        suspicious_metrics = SecurityMetrics.query.filter(
            SecurityMetrics.metric_type == 'suspicious_activity',
            SecurityMetrics.timestamp >= last_24h
        ).all()

        if suspicious_metrics:
            metrics['suspicious_activity'] = {
                'total_incidents': len(suspicious_metrics),
                'avg_score': sum(m.metric_value for m in suspicious_metrics) / len(suspicious_metrics),
                'unique_ips': len(set(m.source_ip for m in suspicious_metrics if m.source_ip))
            }

        return metrics

    def _get_ban_stats(self):
        """Get ban statistics"""
        active_bans = SecurityBan.query.filter_by(is_active=True).all()
        total_bans = SecurityBan.query.count()

        # Bans by type
        bans_by_type = db.session.query(
            SecurityBan.ban_type,
            db.func.count(SecurityBan.id).label('count')
        ).group_by(SecurityBan.ban_type).all()

        # Recent bans (last 7 days)
        last_7d = datetime.utcnow() - timedelta(days=7)
        recent_bans = SecurityBan.query.filter(
            SecurityBan.created_at >= last_7d
        ).count()

        return {
            'active_bans': len(active_bans),
            'total_bans': total_bans,
            'recent_bans': recent_bans,
            'by_type': [{'type': t, 'count': c} for t, c in bans_by_type],
            'expiring_soon': len([
                b for b in active_bans
                if b.expires_at and b.expires_at <= datetime.utcnow() + timedelta(hours=24)
            ])
        }

    def _get_security_trends(self, last_7d):
        """Get security trends over the last 7 days"""
        trends = []

        for i in range(7):
            day_start = last_7d + timedelta(days=i)
            day_end = day_start + timedelta(days=1)

            day_events = SecurityEvent.query.filter(
                SecurityEvent.timestamp >= day_start,
                SecurityEvent.timestamp < day_end
            ).count()

            day_alerts = SecurityAlert.query.filter(
                SecurityAlert.created_at >= day_start,
                SecurityAlert.created_at < day_end
            ).count()

            trends.append({
                'date': day_start.strftime('%Y-%m-%d'),
                'events': day_events,
                'alerts': day_alerts
            })

        return trends

    def _calculate_alert_resolution_rate(self, since):
        """Calculate alert resolution rate"""
        total_alerts = SecurityAlert.query.filter(
            SecurityAlert.created_at >= since
        ).count()

        resolved_alerts = SecurityAlert.query.filter(
            SecurityAlert.created_at >= since,
            SecurityAlert.status.in_(['resolved', 'acknowledged'])
        ).count()

        if total_alerts == 0:
            return 0

        return round((resolved_alerts / total_alerts) * 100, 2)

    def get_real_time_stats(self):
        """Get real-time statistics for dashboard"""
        now = datetime.utcnow()
        last_hour = now - timedelta(hours=1)
        last_minute = now - timedelta(minutes=1)

        return {
            'events_last_hour': SecurityEvent.query.filter(
                SecurityEvent.timestamp >= last_hour
            ).count(),
            'events_last_minute': SecurityEvent.query.filter(
                SecurityEvent.timestamp >= last_minute
            ).count(),
            'active_alerts': SecurityAlert.query.filter_by(status='active').count(),
            'critical_events_last_hour': SecurityEvent.query.filter(
                SecurityEvent.timestamp >= last_hour,
                SecurityEvent.severity == 'critical'
            ).count(),
            'unique_ips_last_hour': db.session.query(SecurityEvent.source_ip).filter(
                SecurityEvent.timestamp >= last_hour
            ).distinct().count(),
            'container_count': self._get_container_count()
        }

    def _get_container_stats(self):
        """Get Docker container statistics"""
        try:
            # Get container count
            result = subprocess.run(['docker', 'ps', '-q'], capture_output=True, text=True)
            container_count = len(result.stdout.strip().split('\n')) if result.stdout.strip() else 0

            # Get container stats
            result = subprocess.run(['docker', 'stats', '--no-stream', '--format', 'json'], capture_output=True, text=True)
            stats = []
            if result.stdout:
                for line in result.stdout.strip().split('\n'):
                    if line:
                        stats.append(json.loads(line))

            return {
                'container_count': container_count,
                'container_limit': 100,  # As per document
                'warning_threshold': 85,
                'containers': stats[:10]  # Top 10 containers
            }
        except Exception:
            return {
                'container_count': 0,
                'container_limit': 100,
                'warning_threshold': 85,
                'containers': []
            }

    def _get_container_count(self):
        """Get current container count"""
        try:
            result = subprocess.run(['docker', 'ps', '-q'], capture_output=True, text=True)
            return len(result.stdout.strip().split('\n')) if result.stdout.strip() else 0
        except Exception:
            return 0

    def check_system_health(self):
        """Check system health and create alerts if needed"""
        # Check container count
        container_count = self._get_container_count()
        if container_count > 100:
            # Create alert for excessive containers
            from ..utils.detector import SecurityDetector
            detector = SecurityDetector()
            detector._create_alert(
                'excessive_containers',
                'high',
                f'Excessive container count: {container_count}',
                f'System is running {container_count} containers, which exceeds the recommended limit of 100',
                source_ip='system'
            )

        # Check disk usage
        try:
            result = subprocess.run(['df', '-h', '/'], capture_output=True, text=True)
            lines = result.stdout.strip().split('\n')
            if len(lines) > 1:
                disk_usage = int(lines[1].split()[4].rstrip('%'))
                if disk_usage > 85:
                    detector = SecurityDetector()
                    detector._create_alert(
                        'high_disk_usage',
                        'high',
                        f'High disk usage: {disk_usage}%',
                        f'Disk usage is at {disk_usage}%, which exceeds the warning threshold of 85%',
                        source_ip='system'
                    )
        except Exception:
            pass

    def _get_flag_attempt_stats(self, last_24h, last_7d):
        """Get flag attempt statistics"""
        from ..models import SecurityEvent

        try:
            # Flag submission events in last 24h
            flag_events_24h = SecurityEvent.query.filter(
                SecurityEvent.event_type.like('flag_submission%'),
                SecurityEvent.timestamp >= last_24h
            ).all()

            # Flag submission events in last 7d
            flag_events_7d = SecurityEvent.query.filter(
                SecurityEvent.event_type.like('flag_submission%'),
                SecurityEvent.timestamp >= last_7d
            ).all()

            # Categorize events
            stats_24h = {
                'total': len(flag_events_24h),
                'success': len([e for e in flag_events_24h if e.event_type == 'flag_submission_success']),
                'failure': len([e for e in flag_events_24h if e.event_type == 'flag_submission_failure']),
                'ratelimited': len([e for e in flag_events_24h if e.event_type == 'flag_submission_ratelimited']),
                'forbidden': len([e for e in flag_events_24h if e.event_type == 'flag_submission_forbidden'])
            }

            stats_7d = {
                'total': len(flag_events_7d),
                'success': len([e for e in flag_events_7d if e.event_type == 'flag_submission_success']),
                'failure': len([e for e in flag_events_7d if e.event_type == 'flag_submission_failure']),
                'ratelimited': len([e for e in flag_events_7d if e.event_type == 'flag_submission_ratelimited']),
                'forbidden': len([e for e in flag_events_7d if e.event_type == 'flag_submission_forbidden'])
            }

            # Top attacking IPs (failed attempts)
            failed_events = [e for e in flag_events_24h if e.event_type == 'flag_submission_failure']
            ip_counts = {}
            for event in failed_events:
                ip = event.source_ip
                ip_counts[ip] = ip_counts.get(ip, 0) + 1

            top_attacking_ips = sorted(ip_counts.items(), key=lambda x: x[1], reverse=True)[:10]

            # Challenge-specific stats
            challenge_stats = {}
            for event in flag_events_24h:
                if event.details and 'challenge_id' in event.details:
                    challenge_id = event.details['challenge_id']
                    if challenge_id not in challenge_stats:
                        challenge_stats[challenge_id] = {
                            'total': 0,
                            'success': 0,
                            'failure': 0
                        }
                    challenge_stats[challenge_id]['total'] += 1
                    if event.event_type == 'flag_submission_success':
                        challenge_stats[challenge_id]['success'] += 1
                    elif event.event_type == 'flag_submission_failure':
                        challenge_stats[challenge_id]['failure'] += 1

            # Calculate success rate
            success_rate_24h = (stats_24h['success'] / stats_24h['total'] * 100) if stats_24h['total'] > 0 else 0
            success_rate_7d = (stats_7d['success'] / stats_7d['total'] * 100) if stats_7d['total'] > 0 else 0

            return {
                'last_24h': stats_24h,
                'last_7d': stats_7d,
                'success_rate_24h': round(success_rate_24h, 2),
                'success_rate_7d': round(success_rate_7d, 2),
                'top_attacking_ips': top_attacking_ips,
                'challenge_stats': challenge_stats,
                'brute_force_detected': len([e for e in flag_events_24h if e.event_type == 'flag_submission_ratelimited']) > 0
            }

        except Exception as e:
            return {
                'last_24h': {'total': 0, 'success': 0, 'failure': 0, 'ratelimited': 0, 'forbidden': 0},
                'last_7d': {'total': 0, 'success': 0, 'failure': 0, 'ratelimited': 0, 'forbidden': 0},
                'success_rate_24h': 0,
                'success_rate_7d': 0,
                'top_attacking_ips': [],
                'challenge_stats': {},
                'brute_force_detected': False,
                'error': str(e)
            }
