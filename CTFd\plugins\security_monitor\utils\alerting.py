import json
import smtplib
import requests
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime
from flask import current_app
from CTFd.utils import get_config
from ..models import SecurityConfig, SecurityAlert

class AlertManager:
    """Manages security alerts and notifications"""
    
    def __init__(self):
        pass
    
    def send_alert(self, alert_type, severity, title, description, source_ip=None, user_id=None):
        """Send security alert via configured channels"""
        alert_data = {
            'alert_type': alert_type,
            'severity': severity,
            'title': title,
            'description': description,
            'source_ip': source_ip,
            'user_id': user_id,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        # Send email alert if enabled
        if SecurityConfig.get_config('security_email_alerts_enabled', False):
            self._send_email_alert(alert_data)
        
        # Send webhook alert if enabled
        if SecurityConfig.get_config('security_webhook_alerts_enabled', False):
            self._send_webhook_alert(alert_data)
        
        # Send Slack alert if enabled
        if SecurityConfig.get_config('security_slack_alerts_enabled', False):
            self._send_slack_alert(alert_data)
        
        return True
    
    def _send_email_alert(self, alert_data):
        """Send email alert"""
        try:
            # Get email configuration
            smtp_server = SecurityConfig.get_config('security_smtp_server', 'localhost')
            smtp_port = SecurityConfig.get_config('security_smtp_port', 587)
            smtp_username = SecurityConfig.get_config('security_smtp_username', '')
            smtp_password = SecurityConfig.get_config('security_smtp_password', '')
            smtp_use_tls = SecurityConfig.get_config('security_smtp_use_tls', True)
            
            from_email = SecurityConfig.get_config('security_alert_from_email', '<EMAIL>')
            to_emails = SecurityConfig.get_config('security_alert_to_emails', [])
            
            if not to_emails:
                return False
            
            # Create email message
            msg = MIMEMultipart()
            msg['From'] = from_email
            msg['To'] = ', '.join(to_emails)
            msg['Subject'] = f"[CTFd Security Alert] {alert_data['severity'].upper()}: {alert_data['title']}"
            
            # Email body
            body = self._format_email_body(alert_data)
            msg.attach(MIMEText(body, 'html'))
            
            # Send email
            server = smtplib.SMTP(smtp_server, smtp_port)
            if smtp_use_tls:
                server.starttls()
            if smtp_username and smtp_password:
                server.login(smtp_username, smtp_password)
            
            server.send_message(msg)
            server.quit()
            
            return True
            
        except Exception as e:
            current_app.logger.error(f"Failed to send email alert: {str(e)}")
            return False
    
    def _send_webhook_alert(self, alert_data):
        """Send webhook alert"""
        try:
            webhook_url = SecurityConfig.get_config('security_webhook_url', '')
            webhook_secret = SecurityConfig.get_config('security_webhook_secret', '')
            
            if not webhook_url:
                return False
            
            # Prepare webhook payload
            payload = {
                'event': 'security_alert',
                'data': alert_data,
                'ctf_name': get_config('ctf_name', 'CTFd'),
                'timestamp': datetime.utcnow().isoformat()
            }
            
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'CTFd-Security-Monitor/1.0'
            }
            
            if webhook_secret:
                headers['X-Security-Secret'] = webhook_secret
            
            # Send webhook
            response = requests.post(
                webhook_url,
                json=payload,
                headers=headers,
                timeout=10
            )
            
            return response.status_code == 200
            
        except Exception as e:
            current_app.logger.error(f"Failed to send webhook alert: {str(e)}")
            return False
    
    def _send_slack_alert(self, alert_data):
        """Send Slack alert"""
        try:
            slack_webhook_url = SecurityConfig.get_config('security_slack_webhook_url', '')
            slack_channel = SecurityConfig.get_config('security_slack_channel', '#security')
            
            if not slack_webhook_url:
                return False
            
            # Determine color based on severity
            color_map = {
                'low': '#36a64f',      # Green
                'medium': '#ff9500',   # Orange
                'high': '#ff0000',     # Red
                'critical': '#8b0000'  # Dark Red
            }
            color = color_map.get(alert_data['severity'], '#ff9500')
            
            # Create Slack message
            payload = {
                'channel': slack_channel,
                'username': 'CTFd Security Monitor',
                'icon_emoji': ':warning:',
                'attachments': [
                    {
                        'color': color,
                        'title': alert_data['title'],
                        'text': alert_data['description'],
                        'fields': [
                            {
                                'title': 'Severity',
                                'value': alert_data['severity'].upper(),
                                'short': True
                            },
                            {
                                'title': 'Alert Type',
                                'value': alert_data['alert_type'],
                                'short': True
                            },
                            {
                                'title': 'Source IP',
                                'value': alert_data.get('source_ip', 'Unknown'),
                                'short': True
                            },
                            {
                                'title': 'Timestamp',
                                'value': alert_data['timestamp'],
                                'short': True
                            }
                        ],
                        'footer': 'CTFd Security Monitor',
                        'ts': int(datetime.utcnow().timestamp())
                    }
                ]
            }
            
            # Send to Slack
            response = requests.post(
                slack_webhook_url,
                json=payload,
                timeout=10
            )
            
            return response.status_code == 200
            
        except Exception as e:
            current_app.logger.error(f"Failed to send Slack alert: {str(e)}")
            return False
    
    def _format_email_body(self, alert_data):
        """Format email body for security alert"""
        severity_colors = {
            'low': '#28a745',
            'medium': '#ffc107',
            'high': '#fd7e14',
            'critical': '#dc3545'
        }
        
        color = severity_colors.get(alert_data['severity'], '#ffc107')
        
        html_body = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }}
                .container {{ max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                .header {{ background-color: {color}; color: white; padding: 20px; text-align: center; }}
                .content {{ padding: 20px; }}
                .alert-info {{ background-color: #f8f9fa; border-left: 4px solid {color}; padding: 15px; margin: 15px 0; }}
                .footer {{ background-color: #f8f9fa; padding: 15px; text-align: center; font-size: 12px; color: #666; }}
                .severity {{ font-weight: bold; text-transform: uppercase; color: {color}; }}
                table {{ width: 100%; border-collapse: collapse; margin: 15px 0; }}
                th, td {{ padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }}
                th {{ background-color: #f8f9fa; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🚨 Security Alert</h1>
                    <p>CTFd Security Monitor</p>
                </div>
                <div class="content">
                    <div class="alert-info">
                        <h2>{alert_data['title']}</h2>
                        <p><strong>Severity:</strong> <span class="severity">{alert_data['severity']}</span></p>
                        <p><strong>Description:</strong> {alert_data['description']}</p>
                    </div>
                    
                    <table>
                        <tr><th>Alert Type</th><td>{alert_data['alert_type']}</td></tr>
                        <tr><th>Source IP</th><td>{alert_data.get('source_ip', 'Unknown')}</td></tr>
                        <tr><th>User ID</th><td>{alert_data.get('user_id', 'Unknown')}</td></tr>
                        <tr><th>Timestamp</th><td>{alert_data['timestamp']}</td></tr>
                    </table>
                    
                    <p><strong>Recommended Actions:</strong></p>
                    <ul>
                        <li>Review the security event details in the CTFd admin panel</li>
                        <li>Check for additional suspicious activity from the source IP</li>
                        <li>Consider implementing additional security measures if needed</li>
                        <li>Monitor for similar patterns or escalation</li>
                    </ul>
                </div>
                <div class="footer">
                    <p>This alert was generated by CTFd Security Monitor</p>
                    <p>Timestamp: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        return html_body
    
    def test_alert_channels(self):
        """Test all configured alert channels"""
        test_alert = {
            'alert_type': 'test_alert',
            'severity': 'low',
            'title': 'Test Security Alert',
            'description': 'This is a test alert to verify notification channels are working correctly.',
            'source_ip': '127.0.0.1',
            'user_id': None,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        results = {}
        
        if SecurityConfig.get_config('security_email_alerts_enabled', False):
            results['email'] = self._send_email_alert(test_alert)
        
        if SecurityConfig.get_config('security_webhook_alerts_enabled', False):
            results['webhook'] = self._send_webhook_alert(test_alert)
        
        if SecurityConfig.get_config('security_slack_alerts_enabled', False):
            results['slack'] = self._send_slack_alert(test_alert)
        
        return results
