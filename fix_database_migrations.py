#!/usr/bin/env python3
"""
Emergency Database Migration Fix Script
Fixes the missing alembic_version table and restores database to working state
"""

import os
import sys
import time
from sqlalchemy import create_engine, text, MetaData, inspect
from sqlalchemy.exc import OperationalError, ProgrammingError

# Database connection settings
DB_HOST = "db"
DB_USER = "ctfd"
DB_PASSWORD = "ctfd"
DB_NAME = "ctfd"
DATABASE_URL = f"mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}/{DB_NAME}"

def wait_for_database(max_attempts=30):
    """Wait for database to be available"""
    print("🔄 Waiting for database to be available...")
    
    for attempt in range(max_attempts):
        try:
            engine = create_engine(DATABASE_URL)
            with engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            print("✅ Database is available!")
            return engine
        except Exception as e:
            print(f"⏳ Attempt {attempt + 1}/{max_attempts}: Database not ready yet...")
            time.sleep(2)
    
    raise Exception("❌ Database failed to become available")

def check_table_exists(engine, table_name):
    """Check if a table exists"""
    try:
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        return table_name in tables
    except Exception:
        return False

def create_alembic_version_table(engine):
    """Create the alembic_version table"""
    print("🔧 Creating alembic_version table...")
    
    try:
        with engine.connect() as conn:
            # Create alembic_version table
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS alembic_version (
                    version_num VARCHAR(32) NOT NULL,
                    PRIMARY KEY (version_num)
                )
            """))
            conn.commit()
            print("✅ alembic_version table created successfully")
            return True
    except Exception as e:
        print(f"❌ Failed to create alembic_version table: {e}")
        return False

def get_latest_migration():
    """Get the latest migration file to determine current version"""
    migrations_dir = "/opt/CTFd/migrations/versions"
    
    try:
        if os.path.exists(migrations_dir):
            migration_files = [f for f in os.listdir(migrations_dir) if f.endswith('.py') and not f.startswith('__')]
            if migration_files:
                # Sort by filename to get the latest
                migration_files.sort()
                latest_file = migration_files[-1]
                # Extract version from filename (first part before underscore)
                version = latest_file.split('_')[0]
                print(f"📋 Found latest migration: {latest_file} (version: {version})")
                return version
    except Exception as e:
        print(f"⚠️ Could not determine latest migration: {e}")
    
    # Fallback to a known working version
    return "8369118943a1"  # Initial revision

def stamp_database(engine, version):
    """Stamp the database with the specified version"""
    print(f"🏷️ Stamping database with version: {version}")
    
    try:
        with engine.connect() as conn:
            # Delete any existing version
            conn.execute(text("DELETE FROM alembic_version"))
            
            # Insert the new version
            conn.execute(text("INSERT INTO alembic_version (version_num) VALUES (:version)"), 
                        {"version": version})
            conn.commit()
            print(f"✅ Database stamped with version: {version}")
            return True
    except Exception as e:
        print(f"❌ Failed to stamp database: {e}")
        return False

def check_core_tables(engine):
    """Check if core CTFd tables exist"""
    core_tables = ['users', 'teams', 'challenges', 'flags', 'submissions', 'solves']
    
    print("🔍 Checking core CTFd tables...")
    missing_tables = []
    
    for table in core_tables:
        if not check_table_exists(engine, table):
            missing_tables.append(table)
    
    if missing_tables:
        print(f"⚠️ Missing core tables: {missing_tables}")
        return False
    else:
        print("✅ All core CTFd tables exist")
        return True

def fix_database():
    """Main function to fix the database"""
    print("🚀 Starting database migration fix...")
    
    try:
        # Wait for database
        engine = wait_for_database()
        
        # Check if alembic_version table exists
        if not check_table_exists(engine, 'alembic_version'):
            print("❌ alembic_version table is missing")
            
            # Create the table
            if not create_alembic_version_table(engine):
                return False
        else:
            print("✅ alembic_version table exists")
        
        # Check core tables
        if not check_core_tables(engine):
            print("❌ Core CTFd tables are missing - database needs full initialization")
            return False
        
        # Get latest migration version
        version = get_latest_migration()
        
        # Stamp the database
        if not stamp_database(engine, version):
            return False
        
        print("🎉 Database migration fix completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Database fix failed: {e}")
        return False

if __name__ == "__main__":
    success = fix_database()
    sys.exit(0 if success else 1)
