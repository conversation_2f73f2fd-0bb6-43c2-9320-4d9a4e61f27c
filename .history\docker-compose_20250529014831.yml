services:
  ctfd:
    build: .
    user: "1000:1000"  # Run as non-root user
    restart: always
    ports:
      - "8000:8000"
    environment:
      - UPLOAD_FOLDER=/var/uploads
      - DATABASE_URL=mysql+pymysql://ctfd:${DB_PASSWORD:-ctfd_user_pass}@db/ctfd
      - REDIS_URL=redis://cache:6379
      - WORKERS=1
      - LOG_FOLDER=/var/log/CTFd
      - ACCESS_LOG=-
      - ERROR_LOG=-
      - REVERSE_PROXY=true
      - PLUGIN_WHITELIST=web_desktop,challenges,dynamic_challenges,flags,ctfd-whale,security_monitor
    volumes:
      - .data/CTFd/logs:/var/log/CTFd
      - .data/CTFd/uploads:/var/uploads
      - .:/opt/CTFd:ro
      - /var/run/docker.sock:/var/run/docker.sock
    depends_on:
      - db
    networks:
        default:
        internal:
        frp:
            ipv4_address: *********
    mem_limit: 450M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service"
    labels:
      - "service=ctfd"
      - "monitor=true"

  nginx:
    image: nginx:1.17
    restart: always
    volumes:
      - ./conf/nginx/http.conf:/etc/nginx/nginx.conf
      - nginx-logs:/var/log/nginx
    ports:
      - 82:80
    depends_on:
      - ctfd
    networks:
        default:
        internal:
    mem_limit: 450M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service"
    labels:
      - "service=nginx"
      - "monitor=true"

  db:
    image: mariadb:10.4.12
    restart: always
    environment:
      - MYSQL_ROOT_PASSWORD=${DB_ROOT_PASSWORD:-ctfd_root_pass}
      - MYSQL_USER=ctfd
      - MYSQL_PASSWORD=${DB_PASSWORD:-ctfd_user_pass}
      - MYSQL_DATABASE=ctfd
    volumes:
      - .data/mysql:/var/lib/mysql
    networks:
        internal:
    # This command is required to set important mariadb defaults
    command: [mysqld, --character-set-server=utf8mb4, --collation-server=utf8mb4_unicode_ci, --wait_timeout=28800, --log-warnings=0]
    mem_limit: 450M

  cache:
    image: redis:4
    restart: always
    volumes:
    - .data/redis:/data
    networks:
        internal:
    mem_limit: 450M

  frps:
    image: glzjin/frp:latest
    restart: always
    volumes:
      - ./frps:/conf/
    entrypoint:
        - /usr/local/bin/frps
        - -c
        - /conf/frps.ini
    ports:
      - "10000-10100:10000-10100"
      - "6490:6490"
    networks:
        frp:
          ipv4_address: *********
        default:

  frpc:
    image: glzjin/frp:latest
    restart: always
    volumes:
      - ./frpc:/conf/
    entrypoint:
        - /usr/local/bin/frpc
        - -c
        - /conf/frpc.ini
    networks:
        frp:
            ipv4_address: *********
        frp-containers:
    mem_limit: 250M

  # Simplified Monitoring Stack - Only Essential Services
  loki:
    image: grafana/loki:2.9.0
    restart: unless-stopped
    ports:
      - "3100:3100"
    volumes:
      - ./conf/loki/loki-config.yaml:/etc/loki/local-config.yaml
      - loki-data:/loki
    command: -config.file=/etc/loki/local-config.yaml
    networks:
      - monitoring
      - internal
    mem_limit: 256m
    healthcheck:
      test: ["CMD-SHELL", "wget --spider --quiet http://localhost:3100/ready || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    labels:
      - "service=loki"
      - "monitor=true"

  promtail:
    image: grafana/promtail:2.9.0
    restart: unless-stopped
    volumes:
      - nginx-logs:/var/log/nginx:ro
      - .data/CTFd/logs:/var/log/CTFd:ro
      - ./conf/promtail/promtail-config.yaml:/etc/promtail/config.yml
    command: -config.file=/etc/promtail/config.yml
    depends_on:
      loki:
        condition: service_healthy
    networks:
      - monitoring
      - internal
    mem_limit: 128m
    labels:
      - "service=promtail"
      - "monitor=true"

  grafana:
    image: grafana/grafana:latest
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_PLUGINS_ALLOW_LOADING_UNSIGNED_PLUGINS=false
      - GF_DEFAULT_APP_MODE=production
      - GF_UNIFIED_ALERTING_ENABLED=false
      - GF_ALERTING_ENABLED=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./conf/grafana/provisioning:/etc/grafana/provisioning
      - ./conf/grafana/dashboards:/var/lib/grafana/dashboards
    depends_on:
      loki:
        condition: service_healthy
    networks:
      - monitoring
      - internal
    mem_limit: 256m
    labels:
      - "service=grafana"
      - "monitor=true"

volumes:
  loki-data:
  grafana-data:  # Fixed: changed from grafana-clean to grafana-data
  prometheus-data:
  nginx-logs:

networks:
    default:
    internal:
        internal: true
    monitoring:
        driver: bridge
        external: false
    frp:
        attachable: true
        driver: bridge
        ipam:
            config:
                - subnet: *********/16
    frp-containers:
        driver: overlay
        internal: false
        attachable: true
        ipam:
            config:
                - subnet: *********/16
