{% extends "admin/base.html" %}

{% block content %}
<div class="jumbotron">
    <div class="container">
        <h1>🚨 Security Alerts</h1>
        <p>Manage and respond to security alerts</p>
    </div>
</div>

<div class="container">
    <!-- Alert Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h4>{{ alerts | selectattr('status', 'equalto', 'active') | selectattr('severity', 'equalto', 'critical') | list | length }}</h4>
                    <p class="mb-0">Critical Active</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h4>{{ alerts | selectattr('status', 'equalto', 'active') | selectattr('severity', 'equalto', 'high') | list | length }}</h4>
                    <p class="mb-0">High Active</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h4>{{ alerts | selectattr('status', 'equalto', 'active') | list | length }}</h4>
                    <p class="mb-0">Total Active</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h4>{{ alerts | selectattr('status', 'equalto', 'resolved') | list | length }}</h4>
                    <p class="mb-0">Resolved</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Alerts Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5>Security Alerts</h5>
            <div>
                <button class="btn btn-sm btn-success" onclick="resolveAllAlerts()">Resolve All Active</button>
                <button class="btn btn-sm btn-danger" onclick="deleteResolvedAlerts()">Delete Resolved</button>
            </div>
        </div>
        <div class="card-body">
            {% if alerts %}
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                            </th>
                            <th>ID</th>
                            <th>Severity</th>
                            <th>Type</th>
                            <th>Title</th>
                            <th>Source IP</th>
                            <th>Count</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Updated</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for alert in alerts %}
                        <tr class="{% if alert.severity == 'critical' %}table-danger{% elif alert.severity == 'high' %}table-warning{% endif %}">
                            <td>
                                <input type="checkbox" class="alert-checkbox" value="{{ alert.id }}">
                            </td>
                            <td>{{ alert.id }}</td>
                            <td>
                                <span class="badge badge-{% if alert.severity == 'critical' %}danger{% elif alert.severity == 'high' %}warning{% elif alert.severity == 'medium' %}info{% else %}secondary{% endif %}">
                                    {{ alert.severity.upper() }}
                                </span>
                            </td>
                            <td>{{ alert.alert_type }}</td>
                            <td>
                                <strong>{{ alert.title }}</strong>
                                {% if alert.description %}
                                <br><small class="text-muted">{{ alert.description | truncate(100) }}</small>
                                {% endif %}
                            </td>
                            <td>
                                {% if alert.source_ip %}
                                <code>{{ alert.source_ip }}</code>
                                <button class="btn btn-sm btn-outline-primary" onclick="showIPDetails('{{ alert.source_ip }}')">
                                    <i class="fas fa-info-circle"></i>
                                </button>
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge badge-primary">{{ alert.event_count }}</span>
                            </td>
                            <td>
                                <span class="badge badge-{% if alert.status == 'active' %}danger{% elif alert.status == 'acknowledged' %}warning{% else %}success{% endif %}">
                                    {{ alert.status.upper() }}
                                </span>
                            </td>
                            <td>{{ alert.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            <td>{{ alert.updated_at.strftime('%Y-%m-%d %H:%M') if alert.updated_at else '-' }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    {% if alert.status == 'active' %}
                                    <button class="btn btn-sm btn-warning" onclick="acknowledgeAlert({{ alert.id }})">
                                        Acknowledge
                                    </button>
                                    <button class="btn btn-sm btn-success" onclick="resolveAlert({{ alert.id }})">
                                        Resolve
                                    </button>
                                    {% elif alert.status == 'acknowledged' %}
                                    <button class="btn btn-sm btn-success" onclick="resolveAlert({{ alert.id }})">
                                        Resolve
                                    </button>
                                    {% endif %}
                                    <button class="btn btn-sm btn-outline-info" onclick="showAlertDetails({{ alert.id }}, '{{ alert.description | replace("'", "\\'") }}')">
                                        Details
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteAlert({{ alert.id }})">
                                        Delete
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Bulk Actions -->
            <div class="mt-3">
                <div class="btn-group" role="group">
                    <button class="btn btn-sm btn-warning" onclick="bulkAcknowledge()">Acknowledge Selected</button>
                    <button class="btn btn-sm btn-success" onclick="bulkResolve()">Resolve Selected</button>
                    <button class="btn btn-sm btn-danger" onclick="bulkDelete()">Delete Selected</button>
                </div>
            </div>
            {% else %}
            <div class="text-center py-4">
                <p class="text-muted">No security alerts found</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Alert Details Modal -->
<div class="modal fade" id="alertDetailsModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Alert Details</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="alertDetailsContent"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- IP Details Modal -->
<div class="modal fade" id="ipDetailsModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">IP Address Details</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="ipDetailsContent">
                Loading...
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.alert-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

function getSelectedAlerts() {
    const checkboxes = document.querySelectorAll('.alert-checkbox:checked');
    return Array.from(checkboxes).map(cb => parseInt(cb.value));
}

function acknowledgeAlert(alertId) {
    updateAlertStatus(alertId, 'acknowledged');
}

function resolveAlert(alertId) {
    updateAlertStatus(alertId, 'resolved');
}

function updateAlertStatus(alertId, status) {
    fetch(`/api/v1/plugins/security_monitor/alerts/${alertId}`, {
        method: 'PATCH',
        headers: {
            'Content-Type': 'application/json',
            'CSRF-Token': "{{ nonce }}"
        },
        body: JSON.stringify({status: status})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error updating alert: ' + data.message);
        }
    });
}

function deleteAlert(alertId) {
    if (confirm('Are you sure you want to delete this alert?')) {
        fetch(`/api/v1/plugins/security_monitor/alerts/${alertId}`, {
            method: 'DELETE',
            headers: {
                'CSRF-Token': "{{ nonce }}"
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error deleting alert: ' + data.message);
            }
        });
    }
}

function bulkAcknowledge() {
    const selectedAlerts = getSelectedAlerts();
    if (selectedAlerts.length === 0) {
        alert('Please select alerts to acknowledge');
        return;
    }

    if (confirm(`Acknowledge ${selectedAlerts.length} selected alerts?`)) {
        bulkUpdateAlerts(selectedAlerts, 'acknowledged');
    }
}

function bulkResolve() {
    const selectedAlerts = getSelectedAlerts();
    if (selectedAlerts.length === 0) {
        alert('Please select alerts to resolve');
        return;
    }

    if (confirm(`Resolve ${selectedAlerts.length} selected alerts?`)) {
        bulkUpdateAlerts(selectedAlerts, 'resolved');
    }
}

function bulkDelete() {
    const selectedAlerts = getSelectedAlerts();
    if (selectedAlerts.length === 0) {
        alert('Please select alerts to delete');
        return;
    }

    if (confirm(`Delete ${selectedAlerts.length} selected alerts?`)) {
        bulkDeleteAlerts(selectedAlerts);
    }
}

function bulkUpdateAlerts(alertIds, status) {
    fetch('/api/v1/plugins/security_monitor/alerts/bulk-update', {
        method: 'PATCH',
        headers: {
            'Content-Type': 'application/json',
            'CSRF-Token': "{{ nonce }}"
        },
        body: JSON.stringify({
            alert_ids: alertIds,
            status: status
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error updating alerts: ' + data.message);
        }
    });
}

function bulkDeleteAlerts(alertIds) {
    fetch('/api/v1/plugins/security_monitor/alerts/bulk-delete', {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
            'CSRF-Token': "{{ nonce }}"
        },
        body: JSON.stringify({alert_ids: alertIds})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error deleting alerts: ' + data.message);
        }
    });
}

function resolveAllAlerts() {
    if (confirm('Resolve all active alerts?')) {
        fetch('/api/v1/plugins/security_monitor/alerts/resolve-all', {
            method: 'POST',
            headers: {
                'CSRF-Token': "{{ nonce }}"
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error resolving alerts: ' + data.message);
            }
        });
    }
}

function deleteResolvedAlerts() {
    if (confirm('Delete all resolved alerts?')) {
        fetch('/api/v1/plugins/security_monitor/alerts/delete-resolved', {
            method: 'DELETE',
            headers: {
                'CSRF-Token': "{{ nonce }}"
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error deleting alerts: ' + data.message);
            }
        });
    }
}

function showAlertDetails(alertId, description) {
    document.getElementById('alertDetailsContent').innerHTML = `
        <p><strong>Alert ID:</strong> ${alertId}</p>
        <p><strong>Description:</strong></p>
        <p>${description}</p>
    `;
    $('#alertDetailsModal').modal('show');
}

function showIPDetails(ip) {
    document.getElementById('ipDetailsContent').innerHTML = 'Loading...';
    $('#ipDetailsModal').modal('show');

    // Fetch IP details (same as in events template)
    fetch(`/api/v1/plugins/security_monitor/ip-details/${ip}`)
        .then(response => response.json())
        .then(data => {
            let html = `
                <p><strong>IP Address:</strong> ${ip}</p>
                <p><strong>Total Events:</strong> ${data.total_events || 0}</p>
                <p><strong>Last Seen:</strong> ${data.last_seen || 'Never'}</p>
                <p><strong>Is Banned:</strong> ${data.is_banned ? 'Yes' : 'No'}</p>
                <p><strong>Event Types:</strong></p>
                <ul>
            `;

            if (data.event_types) {
                for (let [type, count] of Object.entries(data.event_types)) {
                    html += `<li>${type}: ${count}</li>`;
                }
            }

            html += '</ul>';
            document.getElementById('ipDetailsContent').innerHTML = html;
        })
        .catch(error => {
            document.getElementById('ipDetailsContent').innerHTML = 'Error loading IP details';
        });
}
</script>
{% endblock %}
