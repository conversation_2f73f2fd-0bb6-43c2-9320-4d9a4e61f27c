import os
import json
import time
import logging
from datetime import datetime, timedelta
from flask import Blueprint, render_template, request, jsonify, current_app, session, abort
from flask_restx import Namespace, Resource

from CTFd.models import db, Users, Challenges, Submissions, Fails
from CTFd.plugins import (
    register_plugin_assets_directory,
    register_admin_plugin_menu_bar,
    bypass_csrf_protection
)
from CTFd.utils.decorators import authed_only, admins_only
from CTFd.utils import get_config, set_config, user as current_user
from CTFd.utils.user import get_ip
from CTFd.utils.security.csrf import generate_nonce
from CTFd.api import CTFd_API_v1
from CTFd.cache import cache

from .models import SecurityEvent, SecurityConfig, SecurityAlert, SecurityBan, SecurityMetrics, PlatformAnalytics, ContainerMetrics, PerformanceMetrics, create_all
from .utils.detector import SecurityDetector
from .utils.monitor import SecurityMonitor
from .utils.alerting import AlertManager
from .utils.analytics import analytics_collector
from .utils.data_collector import data_collector

def load(app):
    """Load the security monitoring plugin"""
    plugin_name = __name__.split('.')[-1]

    # Initialize database tables
    with app.app_context():
        create_all()

    # Initialize security components
    app.security_detector = SecurityDetector()
    app.security_monitor = SecurityMonitor()
    app.alert_manager = AlertManager()
    app.analytics_collector = analytics_collector
    app.data_collector = data_collector

    # Create local references for use in routes
    security_detector = app.security_detector
    security_monitor = app.security_monitor
    alert_manager = app.alert_manager

    # Start data collection service
    data_collector.start()

    # Register plugin assets
    register_plugin_assets_directory(
        app, base_path=f"/plugins/{plugin_name}/assets",
        endpoint=f'plugins.{plugin_name}.assets'
    )
