"""
Enhanced Security Detector with Advanced DDoS Protection and Threat Intelligence
Based on 2025 security best practices and latest threat trends
"""

import time
import re
import json
import os
import requests
import hashlib
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from flask import request, current_app
from CTFd.cache import cache
from CTFd.models import db
from ..models import SecurityEvent, SecurityConfig, SecurityAlert, SecurityBan, SecurityMetrics

class EnhancedSecurityDetector:
    """Enhanced security threat detection engine with ML-based behavioral analysis"""

    def __init__(self):
        self.request_counts = defaultdict(list)
        self.behavior_patterns = defaultdict(list)
        self.threat_scores = defaultdict(float)
        
        # Enhanced patterns based on 2025 threat landscape
        self.suspicious_patterns = [
            # SQL Injection (enhanced patterns)
            r'(?i)(union|select|insert|delete|drop|create|alter|exec|script)[\s\+\/*\'\"]*[\(\[]',
            r'(?i)(or|and)[\s\+\/*]*[\'\"]?[\d\w]*[\'\"]?[\s\+\/*]*[\=\<\>]',
            r'(?i)(waitfor|delay|benchmark|sleep)[\s\+\/*]*[\(\[]',
            
            # XSS (enhanced patterns)
            r'(?i)(<script[^>]*>|</script>|javascript:|vbscript:|onload\s*=|onerror\s*=)',
            r'(?i)(eval\s*\(|setTimeout\s*\(|setInterval\s*\()',
            r'(?i)(document\.(write|cookie)|window\.(location|open))',
            
            # Path traversal (enhanced)
            r'(?i)(\.\.\/|\.\.\\|\/etc\/|\/proc\/|\/sys\/|\.\.%2f|\.\.%5c)',
            r'(?i)(file:\/\/|ftp:\/\/|gopher:\/\/)',
            
            # Command injection (enhanced)
            r'(?i)(cmd\s*=|exec\s*=|system\s*=|shell\s*=|\||\&\&|\|\|)',
            r'(?i)(whoami|id|pwd|ls|dir|cat|type|echo|ping)',
            
            # Code injection (enhanced)
            r'(?i)(eval\(|base64_decode|gzinflate|preg_replace.*\/e)',
            r'(?i)(php:\/\/|data:\/\/|expect:\/\/)',
            
            # NoSQL Injection
            r'(?i)(\$ne|\$eq|\$gt|\$lt|\$in|\$nin|\$regex)',
            r'(?i)({\s*\$where|\{\s*\$expr)',
            
            # Server-Side Template Injection
            r'(?i)({{.*}}|\[\[.*\]\]|<%.*%>)',
            r'(?i)(__import__|getattr|setattr|delattr)',
            
            # XXE and XML attacks
            r'(?i)(<!entity|<!doctype.*entity)',
            r'(?i)(file:\/\/\/|http:\/\/.*\/dtd)',
            
            # LDAP Injection
            r'(?i)(\*\)|\(\||\)\(|\&\()',
            
            # HTTP Parameter Pollution
            r'(?i)(&\w+=.*&\w+=)',
        ]
        
        # Enhanced bot/scanner patterns for 2025
        self.bot_patterns = [
            # Traditional scanners
            r'(?i)(bot|crawler|spider|scraper|scanner)',
            r'(?i)(nmap|sqlmap|nikto|dirb|gobuster|dirsearch)',
            r'(?i)(burp|zap|acunetix|nessus|openvas)',
            
            # Programming tools (often used by bots)
            r'(?i)(python-requests|curl|wget|httpclient|httpie)',
            r'(?i)(perl|ruby|java|nodejs|go-http)',
            
            # Headless browsers (used by scrapers)
            r'(?i)(phantomjs|selenium|puppeteer|playwright)',
            r'(?i)(headlesschrome|chromeheadless)',
            
            # AI/ML bots (new threat vector)
            r'(?i)(openai|chatgpt|claude|bard|gpt)',
            r'(?i)(ai-agent|ml-bot|auto-agent)',
            
            # Cryptocurrency/blockchain bots
            r'(?i)(bitcoin|ethereum|crypto|blockchain|nft)',
            
            # API testing tools
            r'(?i)(postman|insomnia|httprunner|restassured)',
            
            # Missing or suspicious user agents
            r'^$',  # Empty user agent
            r'^[a-zA-Z]$',  # Single character
            r'(?i)(test|debug|admin|root)',
        ]
        
        # Suspicious endpoints that are commonly targeted
        self.high_risk_endpoints = {
            '/admin': 5,
            '/wp-admin': 8,
            '/login': 3,
            '/admin.php': 8,
            '/phpMyAdmin': 9,
            '/wp-login.php': 8,
            '/.env': 10,
            '/config': 7,
            '/api/admin': 6,
            '/graphql': 4,
            '/swagger': 3,
            '/.git': 9,
            '/backup': 8,
            '/database': 9,
            '/debug': 6,
        }
        
        # Known malicious IP ranges (this would be updated from threat intelligence)
        self.malicious_ip_ranges = [
            # These are examples - in production, this would come from threat feeds
            '10.0.0.0/8',   # Example private range (not actually malicious)
            '**********/12', # Example private range (not actually malicious)
            # Add real threat intelligence IP ranges here
        ]
        
        # Geographic threat scoring
        self.country_threat_scores = {
            'CN': 7,  # High bot traffic
            'RU': 8,  # High attack traffic
            'KP': 10, # Severe threat
            'IR': 7,  # Moderate threat
            'BY': 6,  # Moderate threat
            'PK': 5,  # Some threat activity
            'IN': 3,  # Moderate (lots of legitimate traffic too)
            'BR': 4,  # Moderate
            'TR': 4,  # Moderate
            'VN': 5,  # Moderate
        }

    def detect_advanced_ddos(self, ip, endpoint=None, user_agent=None):
        """
        Advanced DDoS detection with multiple layers and ML-based analysis
        Based on 2025 DDoS trends showing 6.3 Tbps attacks and sophisticated botnets
        """
        if not SecurityConfig.get_config('security_ddos_detection_enabled', True):
            return False

        current_time = time.time()
        
        # Multi-layer DDoS detection
        is_ddos = False
        threat_level = 0
        detection_details = {}

        # Layer 1: Traditional volume-based detection (enhanced thresholds)
        volume_ddos = self._detect_volume_ddos(ip, current_time)
        if volume_ddos['detected']:
            is_ddos = True
            threat_level += volume_ddos['threat_level']
            detection_details.update(volume_ddos['details'])

        # Layer 2: Behavioral analysis (new)
        behavioral_ddos = self._detect_behavioral_ddos(ip, endpoint, user_agent, current_time)
        if behavioral_ddos['detected']:
            is_ddos = True
            threat_level += behavioral_ddos['threat_level']
            detection_details.update(behavioral_ddos['details'])

        # Layer 3: Distributed attack detection (new)
        distributed_ddos = self._detect_distributed_ddos(current_time)
        if distributed_ddos['detected']:
            is_ddos = True
            threat_level += distributed_ddos['threat_level']
            detection_details.update(distributed_ddos['details'])

        # Layer 4: Application-layer DDoS (new)
        app_layer_ddos = self._detect_application_ddos(ip, endpoint, current_time)
        if app_layer_ddos['detected']:
            is_ddos = True
            threat_level += app_layer_ddos['threat_level']
            detection_details.update(app_layer_ddos['details'])

        # Enhanced logging and response
        if is_ddos:
            self._handle_ddos_detection(ip, threat_level, detection_details)
            
        return is_ddos

    def _detect_volume_ddos(self, ip, current_time):
        """Enhanced volume-based DDoS detection with adaptive thresholds"""
        # Adaptive thresholds based on current traffic patterns
        base_threshold = SecurityConfig.get_config('security_ddos_threshold', 100)  # Reduced from 300
        window = SecurityConfig.get_config('security_ddos_window', 60)
        
        # Get historical traffic patterns to adjust threshold
        historical_avg = self._get_historical_traffic_average(ip)
        
        # Adaptive threshold: base_threshold + (historical_avg * 2)
        adaptive_threshold = min(base_threshold + (historical_avg * 2), base_threshold * 3)
        
        cache_key = f"ddos_volume:{ip}"
        request_count = cache.get(cache_key) or 0
        cache.set(cache_key, request_count + 1, timeout=window)

        if request_count >= adaptive_threshold:
            return {
                'detected': True,
                'threat_level': 7,
                'details': {
                    'detection_type': 'volume_ddos',
                    'request_count': request_count,
                    'threshold': adaptive_threshold,
                    'window': window,
                    'adaptive_factor': adaptive_threshold / base_threshold
                }
            }
        
        return {'detected': False, 'threat_level': 0, 'details': {}}

    def _detect_behavioral_ddos(self, ip, endpoint, user_agent, current_time):
        """Behavioral DDoS detection using ML-like pattern analysis"""
        cache_key = f"ddos_behavior:{ip}"
        behavior_data = cache.get(cache_key) or {
            'endpoints': [],
            'user_agents': [],
            'request_intervals': [],
            'start_time': current_time
        }

        # Update behavior data
        if endpoint:
            behavior_data['endpoints'].append(endpoint)
        if user_agent:
            behavior_data['user_agents'].append(user_agent)
        
        # Calculate request interval
        if 'last_request' in behavior_data:
            interval = current_time - behavior_data['last_request']
            behavior_data['request_intervals'].append(interval)
        behavior_data['last_request'] = current_time

        # Keep only recent data (last 300 requests or 10 minutes)
        max_entries = 300
        for key in ['endpoints', 'user_agents', 'request_intervals']:
            if len(behavior_data[key]) > max_entries:
                behavior_data[key] = behavior_data[key][-max_entries:]

        cache.set(cache_key, behavior_data, timeout=600)  # 10 minutes

        # Analyze behavior patterns
        threat_score = 0
        details = {'detection_type': 'behavioral_ddos'}

        # Pattern 1: Endpoint diversity (low diversity = bot-like)
        if len(behavior_data['endpoints']) > 10:
            unique_endpoints = len(set(behavior_data['endpoints']))
            diversity_ratio = unique_endpoints / len(behavior_data['endpoints'])
            if diversity_ratio < 0.1:  # Very low diversity
                threat_score += 4
                details['low_endpoint_diversity'] = diversity_ratio

        # Pattern 2: User-Agent consistency (same UA = bot-like)
        if len(behavior_data['user_agents']) > 5:
            unique_uas = len(set(behavior_data['user_agents']))
            if unique_uas == 1 and len(behavior_data['user_agents']) > 20:
                threat_score += 3
                details['consistent_user_agent'] = True

        # Pattern 3: Request timing patterns (too regular = bot-like)
        if len(behavior_data['request_intervals']) > 10:
            intervals = behavior_data['request_intervals']
            avg_interval = sum(intervals) / len(intervals)
            # Check for overly regular intervals (bots often have consistent timing)
            if avg_interval < 1.0:  # Very fast requests
                regular_count = sum(1 for i in intervals if abs(i - avg_interval) < 0.1)
                if regular_count / len(intervals) > 0.8:  # 80% of requests are regular
                    threat_score += 3
                    details['regular_timing'] = avg_interval

        # Pattern 4: Session duration (long sessions with high activity = suspicious)
        session_duration = current_time - behavior_data['start_time']
        if session_duration > 300 and len(behavior_data['endpoints']) > 100:  # 5+ min, 100+ requests
            threat_score += 2
            details['long_high_activity_session'] = session_duration

        if threat_score >= 5:
            return {
                'detected': True,
                'threat_level': min(threat_score, 10),
                'details': details
            }

        return {'detected': False, 'threat_level': 0, 'details': {}}

    def _detect_distributed_ddos(self, current_time):
        """Detect distributed DDoS attacks across multiple IPs"""
        cache_key = "ddos_distributed_global"
        global_data = cache.get(cache_key) or {
            'ip_requests': defaultdict(int),
            'start_time': current_time,
            'total_requests': 0
        }

        # This would be called for each request, so we increment counters
        # In practice, this would be integrated with the main request handler
        
        # Simulate getting current global request rate
        global_request_rate = self._get_global_request_rate()
        
        # Calculate baseline from historical data
        baseline_rate = self._get_baseline_request_rate()
        
        # Detect if current rate is significantly higher than baseline
        if global_request_rate > baseline_rate * 10:  # 10x baseline
            unique_ips = self._get_unique_ips_last_minute()
            
            # High request rate with many unique IPs suggests distributed attack
            if unique_ips > 50:  # Many different IPs
                return {
                    'detected': True,
                    'threat_level': 8,
                    'details': {
                        'detection_type': 'distributed_ddos',
                        'global_rate': global_request_rate,
                        'baseline_rate': baseline_rate,
                        'unique_ips': unique_ips,
                        'amplification_factor': global_request_rate / baseline_rate
                    }
                }

        return {'detected': False, 'threat_level': 0, 'details': {}}

    def _detect_application_ddos(self, ip, endpoint, current_time):
        """Detect application-layer DDoS targeting specific functionality"""
        if not endpoint:
            return {'detected': False, 'threat_level': 0, 'details': {}}

        # Check for attacks on high-value endpoints
        cache_key = f"ddos_app:{endpoint}"
        endpoint_data = cache.get(cache_key) or {
            'request_count': 0,
            'unique_ips': set(),
            'start_time': current_time
        }

        endpoint_data['request_count'] += 1
        endpoint_data['unique_ips'].add(ip)
        
        cache.set(cache_key, endpoint_data, timeout=300)  # 5 minute window

        # Get endpoint-specific threshold
        endpoint_risk_score = self.high_risk_endpoints.get(endpoint, 1)
        base_threshold = 50
        endpoint_threshold = base_threshold // endpoint_risk_score  # Higher risk = lower threshold

        if endpoint_data['request_count'] > endpoint_threshold:
            # Check if it's from multiple IPs (distributed) or single IP (focused)
            ip_diversity = len(endpoint_data['unique_ips'])
            
            if ip_diversity > 10:  # Distributed attack
                threat_level = 7
            elif ip_diversity == 1:  # Single IP attack
                threat_level = 5
            else:  # Mixed
                threat_level = 6

            return {
                'detected': True,
                'threat_level': threat_level,
                'details': {
                    'detection_type': 'application_ddos',
                    'endpoint': endpoint,
                    'request_count': endpoint_data['request_count'],
                    'threshold': endpoint_threshold,
                    'unique_ips': ip_diversity,
                    'endpoint_risk_score': endpoint_risk_score
                }
            }

        return {'detected': False, 'threat_level': 0, 'details': {}}

    def check_advanced_rate_limit(self, ip, endpoint, user_agent=None, request_size=None):
        """
        Advanced rate limiting with progressive penalties and behavioral analysis
        Based on 2025 WAF best practices
        """
        if not SecurityConfig.get_config('security_rate_limit_enabled', True):
            return False

        current_time = time.time()
        
        # Multi-dimensional rate limiting
        violations = []
        
        # Dimension 1: IP-based rate limiting (traditional)
        ip_violation = self._check_ip_rate_limit(ip, endpoint, current_time)
        if ip_violation:
            violations.append(ip_violation)

        # Dimension 2: User-Agent based rate limiting (new)
        if user_agent:
            ua_violation = self._check_user_agent_rate_limit(user_agent, current_time)
            if ua_violation:
                violations.append(ua_violation)

        # Dimension 3: Endpoint-specific rate limiting (enhanced)
        endpoint_violation = self._check_endpoint_rate_limit(ip, endpoint, current_time)
        if endpoint_violation:
            violations.append(endpoint_violation)

        # Dimension 4: Request size-based limiting (new)
        if request_size:
            size_violation = self._check_size_rate_limit(ip, request_size, current_time)
            if size_violation:
                violations.append(size_violation)

        # Dimension 5: Composite key limiting (IP + endpoint + method)
        composite_violation = self._check_composite_rate_limit(ip, endpoint, current_time)
        if composite_violation:
            violations.append(composite_violation)

        if violations:
            self._handle_rate_limit_violations(ip, endpoint, violations)
            return True

        return False

    def _check_ip_rate_limit(self, ip, endpoint, current_time):
        """Enhanced IP-based rate limiting with progressive penalties"""
        # Get IP's current penalty level
        penalty_level = self._get_ip_penalty_level(ip)
        
        # Base limits adjusted by penalty level
        base_limit = SecurityConfig.get_config('security_rate_limit_requests', 60)
        window = SecurityConfig.get_config('security_rate_limit_window', 60)
        
        # Progressive penalty: each level reduces the limit
        adjusted_limit = max(base_limit // (penalty_level + 1), 5)  # Minimum 5 requests
        
        cache_key = f"rate_limit_ip:{ip}"
        current_count = cache.get(cache_key) or 0
        
        if current_count >= adjusted_limit:
            return {
                'type': 'ip_rate_limit',
                'ip': ip,
                'count': current_count,
                'limit': adjusted_limit,
                'penalty_level': penalty_level,
                'window': window
            }
        
        cache.set(cache_key, current_count + 1, timeout=window)
        return None

    def _check_user_agent_rate_limit(self, user_agent, current_time):
        """User-Agent based rate limiting to prevent bot abuse"""
        # Hash user agent to create a key (for privacy)
        ua_hash = hashlib.md5(user_agent.encode()).hexdigest()[:16]
        cache_key = f"rate_limit_ua:{ua_hash}"
        
        # Lower limits for suspicious user agents
        is_suspicious_ua = any(re.search(pattern, user_agent) for pattern in self.bot_patterns)
        
        if is_suspicious_ua:
            limit = 10  # Very restrictive for bots
            window = 60
        else:
            limit = 100  # More permissive for legitimate clients
            window = 60
        
        current_count = cache.get(cache_key) or 0
        
        if current_count >= limit:
            return {
                'type': 'user_agent_rate_limit',
                'user_agent_hash': ua_hash[:8],  # Partial hash for privacy
                'count': current_count,
                'limit': limit,
                'suspicious': is_suspicious_ua,
                'window': window
            }
        
        cache.set(cache_key, current_count + 1, timeout=window)
        return None

    def _check_endpoint_rate_limit(self, ip, endpoint, current_time):
        """Endpoint-specific rate limiting with different limits per endpoint"""
        if not endpoint:
            return None
            
        # Get endpoint-specific limits
        endpoint_limits = {
            # Authentication endpoints (very restrictive)
            '/login': {'limit': 3, 'window': 300},  # 3 attempts per 5 minutes
            '/register': {'limit': 1, 'window': 3600},  # 1 registration per hour
            '/reset-password': {'limit': 2, 'window': 1800},  # 2 per 30 minutes
            
            # Flag submission endpoints (CTF-specific)
            '/api/v1/challenges': {'limit': 15, 'window': 60},  # 15 per minute
            '/submit': {'limit': 10, 'window': 60},  # 10 per minute
            '/flag': {'limit': 10, 'window': 60},  # 10 per minute
            
            # Admin endpoints (very restrictive)
            '/admin': {'limit': 20, 'window': 60},  # 20 per minute
            '/api/v1/admin': {'limit': 10, 'window': 60},  # 10 per minute
            
            # API endpoints (moderate)
            '/api': {'limit': 60, 'window': 60},  # 60 per minute
            
            # Static assets (permissive)
            '/static': {'limit': 200, 'window': 60},  # 200 per minute
            '/assets': {'limit': 200, 'window': 60},  # 200 per minute
        }
        
        # Find matching endpoint limit
        endpoint_config = None
        for pattern, config in endpoint_limits.items():
            if endpoint.startswith(pattern):
                endpoint_config = config
                break
        
        if not endpoint_config:
            return None  # No specific limit for this endpoint
            
        cache_key = f"rate_limit_endpoint:{ip}:{endpoint}"
        current_count = cache.get(cache_key) or 0
        
        if current_count >= endpoint_config['limit']:
            return {
                'type': 'endpoint_rate_limit',
                'ip': ip,
                'endpoint': endpoint,
                'count': current_count,
                'limit': endpoint_config['limit'],
                'window': endpoint_config['window']
            }
        
        cache.set(cache_key, current_count + 1, timeout=endpoint_config['window'])
        return None

    def _check_size_rate_limit(self, ip, request_size, current_time):
        """Rate limiting based on request size to prevent bandwidth abuse"""
        if request_size < 1024:  # Skip small requests
            return None
            
        cache_key = f"rate_limit_size:{ip}"
        size_data = cache.get(cache_key) or {'total_bytes': 0, 'request_count': 0}
        
        size_data['total_bytes'] += request_size
        size_data['request_count'] += 1
        
        # Limits: 10MB per minute or 100 large requests per minute
        byte_limit = 10 * 1024 * 1024  # 10 MB
        count_limit = 100
        window = 60
        
        cache.set(cache_key, size_data, timeout=window)
        
        if size_data['total_bytes'] > byte_limit or size_data['request_count'] > count_limit:
            return {
                'type': 'size_rate_limit',
                'ip': ip,
                'total_bytes': size_data['total_bytes'],
                'request_count': size_data['request_count'],
                'byte_limit': byte_limit,
                'count_limit': count_limit,
                'window': window
            }
        
        return None

    def _check_composite_rate_limit(self, ip, endpoint, current_time):
        """Composite key rate limiting (IP + endpoint combination)"""
        if not endpoint:
            return None
            
        # Create composite key
        composite_key = f"{ip}:{endpoint}"
        cache_key = f"rate_limit_composite:{hashlib.md5(composite_key.encode()).hexdigest()[:16]}"
        
        # Moderate limits for composite keys
        limit = 30
        window = 60
        
        current_count = cache.get(cache_key) or 0
        
        if current_count >= limit:
            return {
                'type': 'composite_rate_limit',
                'ip': ip,
                'endpoint': endpoint,
                'count': current_count,
                'limit': limit,
                'window': window
            }
        
        cache.set(cache_key, current_count + 1, timeout=window)
        return None

    def detect_advanced_suspicious_activity(self, ip, endpoint, request_obj):
        """
        Advanced suspicious activity detection with threat intelligence and ML-like analysis
        """
        if not SecurityConfig.get_config('security_suspicious_activity_enabled', True):
            return False

        threat_score = 0
        details = {'detection_factors': []}

        # Factor 1: Pattern-based detection (enhanced)
        pattern_score = self._analyze_request_patterns(request_obj)
        threat_score += pattern_score['score']
        if pattern_score['score'] > 0:
            details['detection_factors'].append(pattern_score)

        # Factor 2: Behavioral analysis (new)
        behavior_score = self._analyze_behavioral_patterns(ip, endpoint, request_obj)
        threat_score += behavior_score['score']
        if behavior_score['score'] > 0:
            details['detection_factors'].append(behavior_score)

        # Factor 3: Threat intelligence (new)
        threat_intel_score = self._check_threat_intelligence(ip, request_obj)
        threat_score += threat_intel_score['score']
        if threat_intel_score['score'] > 0:
            details['detection_factors'].append(threat_intel_score)

        # Factor 4: Geographic threat analysis (enhanced)
        geo_score = self._analyze_geographic_threat(ip)
        threat_score += geo_score['score']
        if geo_score['score'] > 0:
            details['detection_factors'].append(geo_score)

        # Factor 5: Request anomaly detection (new)
        anomaly_score = self._detect_request_anomalies(request_obj)
        threat_score += anomaly_score['score']
        if anomaly_score['score'] > 0:
            details['detection_factors'].append(anomaly_score)

        # Factor 6: Timing analysis (new)
        timing_score = self._analyze_request_timing(ip)
        threat_score += timing_score['score']
        if timing_score['score'] > 0:
            details['detection_factors'].append(timing_score)

        # Update threat score for this IP
        self.threat_scores[ip] = max(self.threat_scores[ip], threat_score)

        # Threshold-based detection with confidence levels
        if threat_score >= 15:  # High confidence
            details['confidence'] = 'high'
            details['total_threat_score'] = threat_score
            self._log_suspicious_activity(ip, endpoint, details)
            return True
        elif threat_score >= 8:  # Medium confidence
            details['confidence'] = 'medium'
            details['total_threat_score'] = threat_score
            self._log_suspicious_activity(ip, endpoint, details)
            return True
        elif threat_score >= 5:  # Low confidence (log but don't block)
            details['confidence'] = 'low'
            details['total_threat_score'] = threat_score
            self._log_suspicious_activity(ip, endpoint, details)
            return False

        return False

    def _analyze_request_patterns(self, request_obj):
        """Enhanced pattern analysis with weighted scoring"""
        score = 0
        details = {'type': 'pattern_analysis', 'matches': []}

        full_url = str(request_obj.url)
        query_string = str(request_obj.query_string)
        
        # Check POST data if available
        post_data = ""
        try:
            if request_obj.method == 'POST':
                post_data = str(request_obj.get_data())
        except:
            pass

        combined_data = f"{full_url} {query_string} {post_data}"

        # Weighted pattern matching
        pattern_weights = {
            0: 10,   # SQL injection patterns (highest weight)
            1: 8,    # Advanced SQL patterns
            2: 7,    # Timing attack patterns
            3: 9,    # XSS patterns (high weight)
            4: 6,    # JavaScript execution patterns
            5: 6,    # DOM manipulation patterns
            6: 8,    # Path traversal (high weight)
            7: 5,    # Protocol attacks
            8: 7,    # Command injection (high weight)
            9: 4,    # Common commands
            10: 6,   # Code injection
            11: 5,   # PHP protocol attacks
            12: 4,   # NoSQL injection
            13: 3,   # NoSQL advanced
            14: 5,   # Template injection
            15: 4,   # Python introspection
            16: 6,   # XXE attacks
            17: 4,   # DTD attacks
            18: 3,   # LDAP injection
            19: 2,   # HTTP parameter pollution
        }

        for i, pattern in enumerate(self.suspicious_patterns):
            if re.search(pattern, combined_data):
                pattern_score = pattern_weights.get(i, 3)
                score += pattern_score
                details['matches'].append({
                    'pattern_type': f'pattern_{i}',
                    'score': pattern_score,
                    'pattern': pattern[:50] + '...' if len(pattern) > 50 else pattern
                })

        return {'score': min(score, 15), 'details': details}  # Cap at 15

    def _analyze_behavioral_patterns(self, ip, endpoint, request_obj):
        """Analyze behavioral patterns to detect automated/malicious behavior"""
        score = 0
        details = {'type': 'behavioral_analysis', 'behaviors': []}

        cache_key = f"behavior:{ip}"
        behavior_data = cache.get(cache_key) or {
            'requests': [],
            'endpoints': [],
            'user_agents': [],
            'methods': [],
            'first_seen': time.time()
        }

        # Update behavior data
        current_time = time.time()
        behavior_data['requests'].append(current_time)
        behavior_data['endpoints'].append(endpoint)
        behavior_data['user_agents'].append(request_obj.headers.get('User-Agent', ''))
        behavior_data['methods'].append(request_obj.method)

        # Keep only recent data (last 500 requests or 1 hour)
        cutoff_time = current_time - 3600  # 1 hour
        behavior_data['requests'] = [t for t in behavior_data['requests'] if t > cutoff_time][-500:]
        behavior_data['endpoints'] = behavior_data['endpoints'][-500:]
        behavior_data['user_agents'] = behavior_data['user_agents'][-500:]
        behavior_data['methods'] = behavior_data['methods'][-500:]

        cache.set(cache_key, behavior_data, timeout=3600)

        # Behavior 1: Request frequency analysis
        if len(behavior_data['requests']) > 10:
            intervals = []
            for i in range(1, len(behavior_data['requests'])):
                intervals.append(behavior_data['requests'][i] - behavior_data['requests'][i-1])
            
            avg_interval = sum(intervals) / len(intervals)
            
            # Very fast requests (bot-like)
            if avg_interval < 0.5:
                score += 4
                details['behaviors'].append({'type': 'fast_requests', 'avg_interval': avg_interval})
            
            # Overly regular intervals (bot-like)
            if len(intervals) > 20:
                regular_count = sum(1 for i in intervals if abs(i - avg_interval) < 0.1)
                regularity = regular_count / len(intervals)
                if regularity > 0.8:
                    score += 3
                    details['behaviors'].append({'type': 'regular_timing', 'regularity': regularity})

        # Behavior 2: Endpoint diversity analysis
        if len(behavior_data['endpoints']) > 20:
            unique_endpoints = len(set(behavior_data['endpoints']))
            diversity = unique_endpoints / len(behavior_data['endpoints'])
            
            # Very low diversity (bot scanning specific paths)
            if diversity < 0.05:
                score += 5
                details['behaviors'].append({'type': 'low_endpoint_diversity', 'diversity': diversity})

        # Behavior 3: User-Agent consistency
        if len(behavior_data['user_agents']) > 5:
            unique_uas = len(set(behavior_data['user_agents']))
            
            # Same user agent for many requests (bot-like)
            if unique_uas == 1 and len(behavior_data['user_agents']) > 50:
                score += 3
                details['behaviors'].append({'type': 'consistent_user_agent', 'request_count': len(behavior_data['user_agents'])})

        # Behavior 4: HTTP method patterns
        if len(behavior_data['methods']) > 10:
            method_counts = Counter(behavior_data['methods'])
            
            # Unusual method usage (lots of OPTIONS, HEAD, etc.)
            unusual_methods = ['OPTIONS', 'HEAD', 'TRACE', 'CONNECT']
            unusual_count = sum(method_counts.get(method, 0) for method in unusual_methods)
            if unusual_count > len(behavior_data['methods']) * 0.3:  # 30% unusual methods
                score += 3
                details['behaviors'].append({'type': 'unusual_methods', 'unusual_ratio': unusual_count / len(behavior_data['methods'])})

        # Behavior 5: Session duration with high activity
        session_duration = current_time - behavior_data['first_seen']
        if session_duration > 1800 and len(behavior_data['requests']) > 200:  # 30 min, 200+ requests
            score += 2
            details['behaviors'].append({'type': 'long_high_activity', 'duration': session_duration, 'requests': len(behavior_data['requests'])})

        return {'score': min(score, 10), 'details': details}

    def _check_threat_intelligence(self, ip, request_obj):
        """Check IP against threat intelligence feeds and reputation services"""
        score = 0
        details = {'type': 'threat_intelligence', 'sources': []}

        # Check 1: Known malicious IP ranges (basic implementation)
        # In production, this would integrate with real threat feeds
        for ip_range in self.malicious_ip_ranges:
            if self._ip_in_range(ip, ip_range):
                score += 8
                details['sources'].append({'type': 'malicious_range', 'range': ip_range})
                break

        # Check 2: Tor exit nodes (example - would need real Tor exit list)
        if self._is_tor_exit_node(ip):
            score += 5
            details['sources'].append({'type': 'tor_exit_node'})

        # Check 3: Known botnet IPs (example - would need real botnet feeds)
        if self._is_known_botnet_ip(ip):
            score += 9
            details['sources'].append({'type': 'botnet_ip'})

        # Check 4: Reputation score from cache (simulated)
        reputation_score = self._get_ip_reputation(ip)
        if reputation_score < -5:  # Negative reputation
            score += abs(reputation_score) // 2
            details['sources'].append({'type': 'negative_reputation', 'score': reputation_score})

        # Check 5: Recent security events from this IP
        recent_events = self._get_recent_security_events(ip)
        if recent_events > 10:
            score += min(recent_events // 5, 6)  # Cap at 6 points
            details['sources'].append({'type': 'recent_security_events', 'count': recent_events})

        return {'score': min(score, 12), 'details': details}

    def _analyze_geographic_threat(self, ip):
        """Enhanced geographic threat analysis with country-specific scoring"""
        score = 0
        details = {'type': 'geographic_threat'}

        # Get country code (simplified - in production use GeoIP database)
        country_code = self._get_country_code(ip)
        
        if country_code:
            # Country-based threat scoring
            country_threat_score = self.country_threat_scores.get(country_code, 0)
            score += country_threat_score
            details['country_code'] = country_code
            details['country_threat_score'] = country_threat_score

            # Additional factors
            
            # Check for high-volume countries with low legitimate traffic expectations
            if country_code in ['CN', 'RU', 'KP'] and self._is_ctf_platform():
                # CTF platforms typically don't expect high traffic from these countries
                score += 2
                details['unexpected_geography'] = True

            # Check for requests from multiple countries from same IP (VPN/Proxy)
            if self._check_geographic_inconsistency(ip):
                score += 3
                details['geographic_inconsistency'] = True

        return {'score': min(score, 8), 'details': details}

    def _detect_request_anomalies(self, request_obj):
        """Detect anomalies in request structure and content"""
        score = 0
        details = {'type': 'request_anomalies', 'anomalies': []}

        # Anomaly 1: Missing standard headers
        standard_headers = ['User-Agent', 'Accept', 'Accept-Language', 'Accept-Encoding']
        missing_headers = [h for h in standard_headers if not request_obj.headers.get(h)]
        
        if len(missing_headers) >= 3:
            score += 4
            details['anomalies'].append({'type': 'missing_headers', 'count': len(missing_headers)})

        # Anomaly 2: Unusual header values
        user_agent = request_obj.headers.get('User-Agent', '')
        
        # Very short or very long User-Agent
        if len(user_agent) < 10 or len(user_agent) > 500:
            score += 2
            details['anomalies'].append({'type': 'unusual_user_agent_length', 'length': len(user_agent)})

        # Anomaly 3: Suspicious Accept headers
        accept_header = request_obj.headers.get('Accept', '')
        if accept_header == '*/*' and request_obj.method == 'GET':
            # Too generic for a browser request
            score += 1
            details['anomalies'].append({'type': 'generic_accept_header'})

        # Anomaly 4: Request size anomalies
        content_length = request_obj.headers.get('Content-Length')
        if content_length:
            try:
                length = int(content_length)
                # Very large requests (potential DoS)
                if length > 10 * 1024 * 1024:  # 10MB
                    score += 3
                    details['anomalies'].append({'type': 'large_request', 'size': length})
                # Suspicious small POST requests
                elif request_obj.method == 'POST' and length < 10:
                    score += 1
                    details['anomalies'].append({'type': 'small_post_request', 'size': length})
            except ValueError:
                pass

        # Anomaly 5: HTTP version anomalies
        if hasattr(request_obj, 'environ'):
            http_version = request_obj.environ.get('SERVER_PROTOCOL', '')
            if http_version not in ['HTTP/1.0', 'HTTP/1.1', 'HTTP/2.0']:
                score += 2
                details['anomalies'].append({'type': 'unusual_http_version', 'version': http_version})

        # Anomaly 6: Referrer anomalies
        referrer = request_obj.headers.get('Referer', '')
        if referrer:
            # External referrer for admin/sensitive endpoints
            if '/admin' in request_obj.path and not self._is_internal_referrer(referrer):
                score += 3
                details['anomalies'].append({'type': 'external_admin_referrer'})

        return {'score': min(score, 8), 'details': details}

    def _analyze_request_timing(self, ip):
        """Analyze request timing patterns for bot detection"""
        score = 0
        details = {'type': 'timing_analysis'}

        cache_key = f"timing:{ip}"
        timing_data = cache.get(cache_key) or {'timestamps': [], 'intervals': []}

        current_time = time.time()
        timing_data['timestamps'].append(current_time)

        # Calculate intervals
        if len(timing_data['timestamps']) > 1:
            interval = current_time - timing_data['timestamps'][-2]
            timing_data['intervals'].append(interval)

        # Keep only recent data
        timing_data['timestamps'] = timing_data['timestamps'][-100:]
        timing_data['intervals'] = timing_data['intervals'][-99:]

        cache.set(cache_key, timing_data, timeout=3600)

        if len(timing_data['intervals']) >= 10:
            intervals = timing_data['intervals']
            
            # Statistical analysis of intervals
            avg_interval = sum(intervals) / len(intervals)
            
            # Check for machine-like precision (very consistent timing)
            if len(intervals) > 20:
                variance = sum((i - avg_interval) ** 2 for i in intervals) / len(intervals)
                std_dev = variance ** 0.5
                
                # Very low variance indicates bot-like behavior
                if std_dev < 0.1 and avg_interval < 2.0:
                    score += 4
                    details['consistent_timing'] = {'std_dev': std_dev, 'avg_interval': avg_interval}

            # Check for burst patterns (many requests in very short time)
            recent_intervals = intervals[-10:]
            fast_requests = sum(1 for i in recent_intervals if i < 0.1)
            if fast_requests > 7:  # 7+ requests in < 0.1 seconds
                score += 3
                details['burst_pattern'] = {'fast_requests': fast_requests}

        return {'score': min(score, 5), 'details': details}

    # Helper methods for threat intelligence and analysis
    
    def _get_historical_traffic_average(self, ip):
        """Get historical average traffic for this IP"""
        # Simplified implementation - in practice would query database
        cache_key = f"traffic_history:{ip}"
        history = cache.get(cache_key) or {'requests_per_minute': []}
        
        if history['requests_per_minute']:
            return sum(history['requests_per_minute']) / len(history['requests_per_minute'])
        return 0

    def _get_global_request_rate(self):
        """Get current global request rate"""
        # Simplified implementation
        cache_key = "global_request_rate"
        rate_data = cache.get(cache_key) or {'count': 0, 'minute': int(time.time() // 60)}
        
        current_minute = int(time.time() // 60)
        if current_minute != rate_data['minute']:
            rate_data = {'count': 0, 'minute': current_minute}
        
        rate_data['count'] += 1
        cache.set(cache_key, rate_data, timeout=120)
        
        return rate_data['count']

    def _get_baseline_request_rate(self):
        """Get baseline request rate from historical data"""
        # Simplified implementation - would use actual historical data
        return SecurityConfig.get_config('baseline_request_rate', 50)

    def _get_unique_ips_last_minute(self):
        """Get count of unique IPs in the last minute"""
        # Simplified implementation
        cache_key = "unique_ips_minute"
        ip_data = cache.get(cache_key) or {'ips': set(), 'minute': int(time.time() // 60)}
        
        current_minute = int(time.time() // 60)
        if current_minute != ip_data['minute']:
            ip_data = {'ips': set(), 'minute': current_minute}
        
        # This would be called with actual IPs in practice
        cache.set(cache_key, ip_data, timeout=120)
        
        return len(ip_data['ips'])

    def _get_ip_penalty_level(self, ip):
        """Get current penalty level for IP (0 = no penalty, higher = more restricted)"""
        # Count recent security events for this IP
        try:
            recent_events = SecurityEvent.query.filter(
                SecurityEvent.source_ip == ip,
                SecurityEvent.severity.in_(['high', 'critical']),
                SecurityEvent.timestamp >= datetime.utcnow() - timedelta(hours=24)
            ).count()
            
            if recent_events >= 10:
                return 3  # High penalty
            elif recent_events >= 5:
                return 2  # Medium penalty
            elif recent_events >= 2:
                return 1  # Low penalty
            else:
                return 0  # No penalty
        except Exception:
            return 0

    def _handle_ddos_detection(self, ip, threat_level, details):
        """Handle DDoS detection with appropriate response"""
        try:
            from flask import has_request_context, session
            user_id = session.get('id') if has_request_context() else None
            
            # Create security event
            event = SecurityEvent(
                event_type='ddos_detected',
                severity='critical' if threat_level >= 8 else 'high',
                source_ip=ip,
                user_id=user_id,
                endpoint=request.endpoint if has_request_context() else None,
                method=request.method if has_request_context() else None,
                user_agent=request.headers.get('User-Agent') if has_request_context() else None,
                details=details,
                timestamp=datetime.utcnow()
            )
            
            db.session.add(event)
            db.session.commit()
            
            # Log to file
            self._log_to_json_file(event)
            
            # Create alert
            alert_severity = 'critical' if threat_level >= 8 else 'high'
            alert_title = f'Advanced DDoS Attack Detected from {ip}'
            alert_description = f'Multi-layer DDoS detection triggered with threat level {threat_level}. Detection details: {details}'
            
            self._create_alert('advanced_ddos', alert_severity, alert_title, alert_description, ip, user_id)
            
            # Consider auto-ban for high threat levels
            if threat_level >= 9:
                self._emergency_ban_ip(ip, f'Automatic ban due to severe DDoS attack (threat level {threat_level})')
                
        except Exception as e:
            if current_app:
                current_app.logger.error(f"Failed to handle DDoS detection: {str(e)}")

    def _handle_rate_limit_violations(self, ip, endpoint, violations):
        """Handle rate limit violations with progressive response"""
        try:
            from flask import has_request_context, session
            user_id = session.get('id') if has_request_context() else None
            
            # Calculate total severity
            total_severity = sum(1 for v in violations)
            severity = 'critical' if total_severity >= 3 else 'high' if total_severity >= 2 else 'medium'
            
            # Create security event
            event = SecurityEvent(
                event_type='advanced_rate_limit_exceeded',
                severity=severity,
                source_ip=ip,
                user_id=user_id,
                endpoint=endpoint,
                method=request.method if has_request_context() else None,
                user_agent=request.headers.get('User-Agent') if has_request_context() else None,
                details={'violations': violations, 'violation_count': len(violations)},
                timestamp=datetime.utcnow()
            )
            
            db.session.add(event)
            db.session.commit()
            
            # Log to file
            self._log_to_json_file(event)
            
            # Progressive response based on violation count
            if len(violations) >= 3:
                # Multiple violations - consider temporary ban
                self._temp_ban_ip(ip, f'Multiple rate limit violations: {[v["type"] for v in violations]}')
            elif len(violations) >= 2:
                # Create high-priority alert
                self._create_alert(
                    'multiple_rate_violations',
                    'high',
                    f'Multiple rate limit violations from {ip}',
                    f'IP {ip} violated {len(violations)} different rate limits: {[v["type"] for v in violations]}',
                    ip,
                    user_id
                )
                
        except Exception as e:
            if current_app:
                current_app.logger.error(f"Failed to handle rate limit violations: {str(e)}")

    def _log_suspicious_activity(self, ip, endpoint, details):
        """Log suspicious activity with appropriate severity"""
        try:
            from flask import has_request_context, session
            user_id = session.get('id') if has_request_context() else None
            
            confidence = details.get('confidence', 'medium')
            threat_score = details.get('total_threat_score', 0)
            
            severity = 'critical' if confidence == 'high' else 'high' if confidence == 'medium' else 'medium'
            
            # Create security event
            event = SecurityEvent(
                event_type='advanced_suspicious_activity',
                severity=severity,
                source_ip=ip,
                user_id=user_id,
                endpoint=endpoint,
                method=request.method if has_request_context() else None,
                user_agent=request.headers.get('User-Agent') if has_request_context() else None,
                details=details,
                timestamp=datetime.utcnow()
            )
            
            db.session.add(event)
            db.session.commit()
            
            # Log to file
            self._log_to_json_file(event)
            
            # Create alert for high-confidence detections
            if confidence in ['high', 'medium']:
                self._create_alert(
                    'advanced_suspicious_activity',
                    severity,
                    f'Advanced suspicious activity from {ip}',
                    f'Detected suspicious activity with {confidence} confidence (score: {threat_score}). Factors: {[f["type"] for f in details.get("detection_factors", [])]}',
                    ip,
                    user_id
                )
                
        except Exception as e:
            if current_app:
                current_app.logger.error(f"Failed to log suspicious activity: {str(e)}")

    # Placeholder methods for threat intelligence (would be implemented with real services)
    
    def _ip_in_range(self, ip, ip_range):
        """Check if IP is in CIDR range (simplified implementation)"""
        # In production, use proper CIDR matching library
        return False  # Placeholder

    def _is_tor_exit_node(self, ip):
        """Check if IP is a Tor exit node"""
        # In production, integrate with Tor exit node list
        return False  # Placeholder

    def _is_known_botnet_ip(self, ip):
        """Check if IP is known botnet member"""
        # In production, integrate with botnet IP feeds
        return False  # Placeholder

    def _get_ip_reputation(self, ip):
        """Get IP reputation score from threat intelligence"""
        # In production, integrate with reputation services
        return 0  # Placeholder

    def _get_recent_security_events(self, ip):
        """Get count of recent security events from this IP"""
        try:
            return SecurityEvent.query.filter(
                SecurityEvent.source_ip == ip,
                SecurityEvent.timestamp >= datetime.utcnow() - timedelta(hours=24)
            ).count()
        except Exception:
            return 0

    def _get_country_code(self, ip):
        """Get country code for IP (simplified)"""
        # In production, use GeoIP database
        # For now, return None to disable geographic features
        return None

    def _is_ctf_platform(self):
        """Check if this is a CTF platform (always True for this implementation)"""
        return True

    def _check_geographic_inconsistency(self, ip):
        """Check for geographic inconsistencies (VPN/proxy detection)"""
        # In production, track IP geolocation over time
        return False  # Placeholder

    def _is_internal_referrer(self, referrer):
        """Check if referrer is internal to the site"""
        try:
            from urllib.parse import urlparse
            parsed = urlparse(referrer)
            # Compare with current site domain
            return parsed.netloc in ['localhost', '127.0.0.1'] or 'ctfd' in parsed.netloc.lower()
        except Exception:
            return False

    def _emergency_ban_ip(self, ip, reason):
        """Emergency ban for severe threats"""
        try:
            ban = SecurityBan(
                ip_address=ip,
                reason=reason,
                ban_type='permanent',
                expires_at=None,
                created_by=None
            )
            db.session.add(ban)
            db.session.commit()
        except Exception as e:
            if current_app:
                current_app.logger.error(f"Failed to emergency ban IP {ip}: {str(e)}")

    def _temp_ban_ip(self, ip, reason):
        """Temporary ban for moderate threats"""
        try:
            ban = SecurityBan(
                ip_address=ip,
                reason=reason,
                ban_type='temporary',
                expires_at=datetime.utcnow() + timedelta(hours=6),  # 6 hour ban
                created_by=None
            )
            db.session.add(ban)
            db.session.commit()
        except Exception as e:
            if current_app:
                current_app.logger.error(f"Failed to temp ban IP {ip}: {str(e)}")

    def _create_alert(self, alert_type, severity, title, description, source_ip=None, user_id=None):
        """Create a security alert"""
        try:
            alert = SecurityAlert(
                alert_type=alert_type,
                severity=severity,
                title=title,
                description=description,
                source_ip=source_ip,
                user_id=user_id
            )
            db.session.add(alert)
            db.session.commit()
        except Exception as e:
            if current_app:
                current_app.logger.error(f"Failed to create alert: {str(e)}")

    def _log_to_json_file(self, event):
        """Log security event to JSON file"""
        try:
            log_dir = '/var/log/CTFd'
            log_file = os.path.join(log_dir, 'security.log')

            os.makedirs(log_dir, exist_ok=True)

            log_entry = {
                'timestamp': event.timestamp.isoformat(),
                'event_type': event.event_type,
                'severity': event.severity,
                'ip': event.source_ip,
                'user_id': event.user_id,
                'endpoint': event.endpoint,
                'method': event.method,
                'user_agent': event.user_agent[:200] if event.user_agent else None,
                'details': event.details or {}
            }

            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(log_entry) + '\n')
                f.flush()
                
        except Exception as e:
            if current_app:
                current_app.logger.error(f"Failed to write security log: {str(e)}")
            try:
                print(f"SECURITY_EVENT: {event.event_type} from {event.source_ip}")
            except:
                pass
