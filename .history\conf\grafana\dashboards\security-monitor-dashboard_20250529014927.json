{"id": null, "uid": "security-monitor-real-data", "title": "🛡️ CTF Security Monitor - Checkpoint 4 Restored", "tags": ["security", "monitoring", "ctf", "checkpoint4"], "style": "dark", "timezone": "browser", "editable": true, "hideControls": false, "graphTooltip": 1, "time": {"from": "now-6h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "refresh": "30s", "panels": [{"id": 1, "title": "🛡️ SECURITY OVERVIEW", "type": "row", "collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}}, {"id": 2, "title": "🚨 Active Security Alerts", "type": "stat", "targets": [{"datasource": {"type": "mysql", "uid": "ctfd-database"}, "rawSql": "SELECT COUNT(*) as value FROM security_alerts WHERE status = 'active'", "format": "table", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}, "unit": "short", "custom": {"displayMode": "lcd-gauge"}}}, "gridPos": {"h": 4, "w": 4, "x": 0, "y": 1}}, {"id": 3, "title": "🔒 Banned IPs", "type": "stat", "targets": [{"datasource": {"type": "mysql", "uid": "ctfd-database"}, "rawSql": "SELECT COUNT(*) as value FROM security_bans WHERE is_active = 1", "format": "table", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short", "custom": {"displayMode": "gradient-gauge"}}}, "gridPos": {"h": 4, "w": 4, "x": 4, "y": 1}}, {"id": 4, "title": "⚡ Security Events (Last Hour)", "type": "stat", "targets": [{"datasource": {"type": "mysql", "uid": "ctfd-database"}, "rawSql": "SELECT COUNT(*) as value FROM security_events WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 1 HOUR)", "format": "table", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short", "custom": {"displayMode": "gradient-gauge"}}}, "gridPos": {"h": 4, "w": 4, "x": 8, "y": 1}}, {"id": 5, "title": "👥 Active Users", "type": "stat", "targets": [{"datasource": {"type": "mysql", "uid": "ctfd-database"}, "rawSql": "SELECT COUNT(*) as value FROM users WHERE hidden = 0", "format": "table", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short", "custom": {"displayMode": "gradient-gauge"}}}, "gridPos": {"h": 4, "w": 4, "x": 12, "y": 1}}, {"id": 6, "title": "📊 Total Security Events", "type": "stat", "targets": [{"datasource": {"type": "mysql", "uid": "ctfd-database"}, "rawSql": "SELECT COUNT(*) as value FROM security_events", "format": "table", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short", "custom": {"displayMode": "gradient-gauge"}}}, "gridPos": {"h": 4, "w": 4, "x": 16, "y": 1}}, {"id": 7, "title": "💾 System Health", "type": "stat", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "up{job=~\"prometheus|node|loki|grafana\"}", "format": "time_series", "refId": "A", "legendFormat": "{{job}}"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}, "mappings": [{"options": {"0": {"text": "DOWN"}}, "type": "value"}, {"options": {"1": {"text": "UP"}}, "type": "value"}], "unit": "short", "noValue": "No Data"}}, "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "orientation": "horizontal", "textMode": "value_and_name", "colorMode": "background", "graphMode": "none", "justifyMode": "center"}, "gridPos": {"h": 4, "w": 4, "x": 20, "y": 1}}, {"id": 8, "title": "🔍 REAL-TIME SECURITY EVENTS", "type": "row", "collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 5}}, {"id": 9, "title": "🚨 Security Events by Type", "type": "timeseries", "targets": [{"datasource": {"type": "mysql", "uid": "ctfd-database"}, "rawSql": "SELECT UNIX_TIMESTAMP(DATE_FORMAT(timestamp, '%Y-%m-%d %H:%i:00')) * 1000 as time, event_type as metric, COUNT(*) as value FROM security_events WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY) GROUP BY DATE_FORMAT(timestamp, '%Y-%m-%d %H:%i:00'), event_type ORDER BY time", "format": "time_series", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineWidth": 2, "fillOpacity": 20, "pointSize": 4}, "unit": "short"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 6}}, {"id": 11, "title": "🎯 Top Source IPs (Security Events)", "type": "table", "targets": [{"datasource": {"type": "mysql", "uid": "ctfd-database"}, "rawSql": "SELECT source_ip as 'Source IP', COUNT(*) as 'Event Count', MAX(severity) as 'Max Severity', MAX(timestamp) as 'Last Event' FROM security_events WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY) GROUP BY source_ip ORDER BY COUNT(*) DESC LIMIT 15", "format": "table", "refId": "A"}], "fieldConfig": {"defaults": {"custom": {"align": "auto", "displayMode": "color-background"}, "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10}, {"color": "red", "value": 50}]}}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 14}}, {"id": 12, "title": "🏆 CTF OPERATIONS", "type": "row", "collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 22}}, {"id": 13, "title": "📊 Recent Submissions", "type": "timeseries", "targets": [{"datasource": {"type": "mysql", "uid": "ctfd-database"}, "rawSql": "SELECT UNIX_TIMESTAMP(DATE_FORMAT(date, '%Y-%m-%d %H:%i:00')) * 1000 as time, 'submissions' as metric, COUNT(*) as value FROM submissions GROUP BY DATE_FORMAT(date, '%Y-%m-%d %H:%i:00') ORDER BY time", "format": "time_series", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineWidth": 2, "fillOpacity": 30, "pointSize": 4}, "unit": "short"}}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 23}}, {"id": 14, "title": "✅ Success Rate", "type": "stat", "targets": [{"datasource": {"type": "mysql", "uid": "ctfd-database"}, "rawSql": "SELECT ROUND((SELECT COUNT(*) FROM solves) / NULLIF((SELECT COUNT(*) FROM submissions), 0) * 100, 1) as value", "format": "table", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 20}, {"color": "green", "value": 40}]}, "unit": "percent", "custom": {"displayMode": "lcd-gauge"}}}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 23}}, {"id": 15, "title": "🏆 Top Teams", "type": "table", "targets": [{"datasource": {"type": "mysql", "uid": "ctfd-database"}, "rawSql": "SELECT t.name as 'Team', COALESCE(SUM(c.value), 0) as 'Score', COUNT(s.id) as 'Solves' FROM teams t LEFT JOIN solves s ON t.id = s.team_id LEFT JOIN challenges c ON s.challenge_id = c.id WHERE t.hidden = 0 GROUP BY t.id, t.name ORDER BY Score DESC LIMIT 10", "format": "table", "refId": "A"}], "fieldConfig": {"defaults": {"custom": {"displayMode": "gradient-gauge"}, "color": {"mode": "continuous-BlPu"}}}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 23}}, {"id": 16, "title": "⚡ SYSTEM PERFORMANCE", "type": "row", "collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 31}}, {"id": 17, "title": "💾 System Memory Usage", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes", "format": "time_series", "legendFormat": "{{name}}", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "decbytes", "custom": {"drawStyle": "line", "lineWidth": 2, "fillOpacity": 15, "pointSize": 3}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 32}}, {"id": 18, "title": "🔥 System CPU Usage", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "expr": "100 - (avg by (instance) (rate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)", "format": "time_series", "legendFormat": "{{name}}", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "percent", "custom": {"drawStyle": "line", "lineWidth": 2, "fillOpacity": 15, "pointSize": 3}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 32}}, {"id": 19, "title": "🔴 LIVE LOG STREAMS", "type": "row", "collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 40}}, {"id": 20, "title": "🛡️ Security Events Stream", "type": "logs", "targets": [{"datasource": {"type": "loki", "uid": "loki"}, "expr": "{service=\"ctfd\", log_type=\"security\"}", "refId": "A"}], "options": {"showTime": true, "showLabels": false, "showCommonLabels": false, "wrapLogMessage": true, "sortOrder": "Descending", "maxLines": 50, "enableLogDetails": true, "dedupStrategy": "none"}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 41}}, {"id": 21, "title": "🔐 Authentication Events", "type": "logs", "targets": [{"datasource": {"type": "loki", "uid": "loki"}, "expr": "{service=\"ctfd\", log_type=\"logins\"}", "refId": "A"}], "options": {"showTime": true, "showLabels": false, "showCommonLabels": false, "wrapLogMessage": true, "sortOrder": "Descending", "maxLines": 50, "enableLogDetails": true, "dedupStrategy": "none"}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 41}}]}