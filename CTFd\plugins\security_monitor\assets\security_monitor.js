// Security Monitor Plugin JavaScript

class SecurityMonitor {
    constructor() {
        this.refreshInterval = 30000; // 30 seconds
        this.autoRefreshEnabled = true;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.startAutoRefresh();
        this.initializeCharts();
    }

    setupEventListeners() {
        // Auto-refresh toggle
        const autoRefreshToggle = document.getElementById('autoRefreshToggle');
        if (autoRefreshToggle) {
            autoRefreshToggle.addEventListener('change', (e) => {
                this.autoRefreshEnabled = e.target.checked;
                if (this.autoRefreshEnabled) {
                    this.startAutoRefresh();
                } else {
                    this.stopAutoRefresh();
                }
            });
        }

        // Refresh button
        const refreshButton = document.getElementById('refreshButton');
        if (refreshButton) {
            refreshButton.addEventListener('click', () => {
                this.refreshDashboard();
            });
        }

        // Export buttons
        const exportEventsButton = document.getElementById('exportEvents');
        if (exportEventsButton) {
            exportEventsButton.addEventListener('click', () => {
                this.exportEvents();
            });
        }

        const exportAlertsButton = document.getElementById('exportAlerts');
        if (exportAlertsButton) {
            exportAlertsButton.addEventListener('click', () => {
                this.exportAlerts();
            });
        }
    }

    startAutoRefresh() {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
        }
        
        this.refreshTimer = setInterval(() => {
            if (this.autoRefreshEnabled) {
                this.refreshDashboard();
            }
        }, this.refreshInterval);
    }

    stopAutoRefresh() {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
            this.refreshTimer = null;
        }
    }

    refreshDashboard() {
        // Show loading indicator
        this.showLoadingIndicator();
        
        // Refresh statistics
        this.refreshStats();
        
        // Refresh charts
        this.refreshCharts();
        
        // Refresh recent events
        this.refreshRecentEvents();
        
        // Hide loading indicator
        setTimeout(() => {
            this.hideLoadingIndicator();
        }, 1000);
    }

    showLoadingIndicator() {
        const indicator = document.getElementById('loadingIndicator');
        if (indicator) {
            indicator.style.display = 'inline-block';
        }
    }

    hideLoadingIndicator() {
        const indicator = document.getElementById('loadingIndicator');
        if (indicator) {
            indicator.style.display = 'none';
        }
    }

    refreshStats() {
        fetch('/api/v1/plugins/security_monitor/stats')
            .then(response => response.json())
            .then(data => {
                this.updateStatsCards(data);
            })
            .catch(error => {
                console.error('Error refreshing stats:', error);
            });
    }

    updateStatsCards(stats) {
        // Update overview cards
        const elements = {
            'totalEvents24h': stats.overview?.total_events_24h || 0,
            'activeAlerts': stats.overview?.active_alerts || 0,
            'criticalEvents24h': stats.overview?.critical_events_24h || 0,
            'activeBans': stats.overview?.active_bans || 0
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        });
    }

    refreshCharts() {
        // This would refresh chart data if charts are present
        if (window.eventsTypeChart) {
            // Refresh events type chart
            fetch('/api/v1/plugins/security_monitor/stats')
                .then(response => response.json())
                .then(data => {
                    if (data.events && data.events.by_type) {
                        window.eventsTypeChart.data.labels = data.events.by_type.map(item => item.type);
                        window.eventsTypeChart.data.datasets[0].data = data.events.by_type.map(item => item.count);
                        window.eventsTypeChart.update();
                    }
                });
        }

        if (window.eventsSeverityChart) {
            // Refresh events severity chart
            fetch('/api/v1/plugins/security_monitor/stats')
                .then(response => response.json())
                .then(data => {
                    if (data.events && data.events.by_severity) {
                        window.eventsSeverityChart.data.labels = data.events.by_severity.map(item => item.severity);
                        window.eventsSeverityChart.data.datasets[0].data = data.events.by_severity.map(item => item.count);
                        window.eventsSeverityChart.update();
                    }
                });
        }
    }

    refreshRecentEvents() {
        fetch('/api/v1/plugins/security_monitor/events?limit=15')
            .then(response => response.json())
            .then(data => {
                this.updateRecentEventsTable(data.events);
            })
            .catch(error => {
                console.error('Error refreshing recent events:', error);
            });
    }

    updateRecentEventsTable(events) {
        const tbody = document.querySelector('#recentEventsTable tbody');
        if (!tbody || !events) return;

        tbody.innerHTML = '';
        
        events.forEach(event => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${new Date(event.timestamp).toLocaleTimeString()}</td>
                <td>${event.event_type}</td>
                <td><span class="badge badge-${this.getSeverityClass(event.severity)}">${event.severity}</span></td>
                <td>${event.source_ip}</td>
                <td>${event.endpoint || 'N/A'}</td>
                <td>${event.details ? JSON.stringify(event.details).substring(0, 50) + '...' : '-'}</td>
            `;
            tbody.appendChild(row);
        });
    }

    getSeverityClass(severity) {
        const classes = {
            'critical': 'danger',
            'high': 'warning',
            'medium': 'info',
            'low': 'secondary'
        };
        return classes[severity] || 'secondary';
    }

    initializeCharts() {
        // Initialize real-time charts if Chart.js is available
        if (typeof Chart !== 'undefined') {
            this.initializeRealTimeChart();
        }
    }

    initializeRealTimeChart() {
        const ctx = document.getElementById('realTimeChart');
        if (!ctx) return;

        this.realTimeChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'Events per Minute',
                    data: [],
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });

        // Update real-time chart every minute
        setInterval(() => {
            this.updateRealTimeChart();
        }, 60000);
    }

    updateRealTimeChart() {
        if (!this.realTimeChart) return;

        fetch('/api/v1/plugins/security_monitor/real-time-stats')
            .then(response => response.json())
            .then(data => {
                const now = new Date();
                const timeLabel = now.toLocaleTimeString();
                
                // Add new data point
                this.realTimeChart.data.labels.push(timeLabel);
                this.realTimeChart.data.datasets[0].data.push(data.events_last_minute || 0);
                
                // Keep only last 20 data points
                if (this.realTimeChart.data.labels.length > 20) {
                    this.realTimeChart.data.labels.shift();
                    this.realTimeChart.data.datasets[0].data.shift();
                }
                
                this.realTimeChart.update();
            })
            .catch(error => {
                console.error('Error updating real-time chart:', error);
            });
    }

    exportEvents() {
        const params = new URLSearchParams(window.location.search);
        params.set('export', 'csv');
        
        const exportUrl = `/plugins/security_monitor/admin/events?${params.toString()}`;
        window.open(exportUrl, '_blank');
    }

    exportAlerts() {
        const exportUrl = '/plugins/security_monitor/admin/alerts?export=csv';
        window.open(exportUrl, '_blank');
    }

    // Utility functions
    static formatTimestamp(timestamp) {
        return new Date(timestamp).toLocaleString();
    }

    static formatDuration(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        
        if (hours > 0) {
            return `${hours}h ${minutes}m ${secs}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${secs}s`;
        } else {
            return `${secs}s`;
        }
    }

    static showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show`;
        notification.style.position = 'fixed';
        notification.style.top = '20px';
        notification.style.right = '20px';
        notification.style.zIndex = '9999';
        notification.innerHTML = `
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        `;
        
        document.body.appendChild(notification);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
    }
}

// Initialize Security Monitor when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    if (document.querySelector('.security-dashboard')) {
        window.securityMonitor = new SecurityMonitor();
    }
});

// Global utility functions for templates
window.SecurityMonitorUtils = {
    formatTimestamp: SecurityMonitor.formatTimestamp,
    formatDuration: SecurityMonitor.formatDuration,
    showNotification: SecurityMonitor.showNotification
};
