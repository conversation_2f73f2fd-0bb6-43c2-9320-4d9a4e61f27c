"""
Enhanced Security Monitor Plugin - Main Module
Integrates all advanced security components for comprehensive CTF platform protection
Version 2.0.0 - 2025 Security Best Practices
"""

import os
import json
import time
import logging
from datetime import datetime, timedelta
from flask import Blueprint, render_template, request, jsonify, current_app, session, abort
from flask_restx import Namespace, Resource

from CTFd.models import db, Users, Challenges, Submissions, Fails
from CTFd.plugins import (
    register_plugin_assets_directory,
    register_admin_plugin_menu_bar,
    bypass_csrf_protection
)
from CTFd.utils.decorators import authed_only, admins_only
from CTFd.utils import get_config, set_config, user as current_user
from CTFd.utils.user import get_ip
from CTFd.utils.security.csrf import generate_nonce
from CTFd.api import CTFd_API_v1
from CTFd.cache import cache

# Import enhanced security components
from .models import SecurityEvent, SecurityConfig, SecurityAlert, SecurityBan, SecurityMetrics, PlatformAnalytics, ContainerMetrics, PerformanceMetrics, create_all
from .utils.enhanced_detector import EnhancedSecurityDetector
from .utils.enhanced_alerting import EnhancedAlertManager
from .utils.ctf_api_security import CTFAPISecurityManager
from .utils.enhanced_config import EnhancedSecurityConfig
from .utils.monitor import SecurityMonitor
from .utils.analytics import analytics_collector
from .utils.data_collector import data_collector
from .metrics import register_metrics_endpoint

def load(app):
    """Load the enhanced security monitoring plugin"""
    plugin_name = __name__.split('.')[-1]
    
    print(f" * Loading Enhanced Security Monitor v2.0.0...")

    # Initialize database tables
    with app.app_context():
        if not create_all():
            print(f" * WARNING: Some database tables could not be created")

    # Initialize enhanced security components
    try:
        app.enhanced_security_detector = EnhancedSecurityDetector()
        app.enhanced_alert_manager = EnhancedAlertManager()
        app.ctf_api_security = CTFAPISecurityManager()
        app.security_monitor = SecurityMonitor()
        app.analytics_collector = analytics_collector
        app.data_collector = data_collector
        
        print(f" * Enhanced security components initialized")
        
    except Exception as e:
        print(f" * ERROR: Failed to initialize security components: {str(e)}")
        # Fallback to basic components
        from .utils.detector import SecurityDetector
        from .utils.alerting import AlertManager
        app.enhanced_security_detector = SecurityDetector()
        app.enhanced_alert_manager = AlertManager()
        app.ctf_api_security = None
        app.security_monitor = SecurityMonitor()

    # Create local references for use in routes
    enhanced_detector = app.enhanced_security_detector
    enhanced_alerting = app.enhanced_alert_manager
    ctf_api_security = app.ctf_api_security
    security_monitor = app.security_monitor

    # Initialize enhanced configuration if not already done
    with app.app_context():
        try:
            if not SecurityConfig.get_config('enhanced_security_initialized_at'):
                print(f" * Initializing enhanced security configuration...")
                EnhancedSecurityConfig.initialize_enhanced_config()
                
                # Apply balanced profile by default
                EnhancedSecurityConfig.apply_security_profile('balanced')
                print(f" * Applied 'balanced' security profile")
            else:
                print(f" * Enhanced security configuration already initialized")
                
        except Exception as e:
            print(f" * WARNING: Failed to initialize enhanced configuration: {str(e)}")

    # Start data collection service
    try:
        data_collector.start()
        print(f" * Data collection service started")
    except Exception as e:
        print(f" * WARNING: Data collection service failed to start: {str(e)}")

    # Register plugin assets
    register_plugin_assets_directory(
        app, base_path=f"/plugins/{plugin_name}/assets",
        endpoint=f'plugins.{plugin_name}.assets'
    )

    # Create enhanced blueprint for admin interface
    admin_blueprint = Blueprint(
        "enhanced_security_monitor_admin",
        __name__,
        template_folder="templates",
        static_folder="assets",
        url_prefix="/plugins/security_monitor/admin"
    )

    # Create enhanced API namespace
    security_namespace = Namespace(
        "enhanced_security_monitor",
        description="Enhanced Security Monitoring API v2.0",
        path="/plugins/security_monitor"
    )

    # Enhanced Admin Routes
    
    @admin_blueprint.route('/')
    @admin_blueprint.route('/dashboard')
    @admins_only
    def enhanced_dashboard():
        """Enhanced security monitoring dashboard"""
        try:
            # Get comprehensive security statistics
            stats = security_monitor.get_security_stats()
            
            # Get recent security events with enhanced details
            recent_events = SecurityEvent.query.order_by(
                SecurityEvent.timestamp.desc()
            ).limit(100).all()

            # Get active alerts with grouping information
            active_alerts = SecurityAlert.query.filter_by(
                status='active'
            ).order_by(SecurityAlert.created_at.desc()).limit(50).all()
            
            # Get configuration summary
            config_summary = EnhancedSecurityConfig.get_configuration_summary()
            
            # Get API security stats if enabled
            api_stats = {}
            if ctf_api_security:
                api_stats = ctf_api_security.get_api_security_stats()
            
            # Get alert statistics
            alert_stats = enhanced_alerting.get_alert_statistics()
            
            return render_template(
                'enhanced_security_dashboard.html',
                events=recent_events,
                stats=stats,
                alerts=active_alerts,
                config_summary=config_summary,
                api_stats=api_stats,
                alert_stats=alert_stats,
                nonce=generate_nonce()
            )
            
        except Exception as e:
            current_app.logger.error(f"Enhanced dashboard error: {str(e)}")
            return render_template('error.html', error="Dashboard loading failed"), 500

    @admin_blueprint.route('/config', methods=['GET', 'POST'])
    @admins_only
    def enhanced_config():
        """Enhanced security configuration panel"""
        if request.method == 'POST':
            try:
                data = request.get_json() or request.form
                
                # Handle profile application
                if 'apply_profile' in data:
                    profile_name = data.get('profile_name', 'balanced')
                    result = EnhancedSecurityConfig.apply_security_profile(profile_name)
                    return jsonify({
                        'success': result,
                        'message': f'Applied {profile_name} security profile'
                    })
                
                # Handle configuration reset
                if 'reset_config' in data:
                    result = EnhancedSecurityConfig.reset_to_defaults()
                    return jsonify(result)
                
                # Handle configuration import
                if 'import_config' in data:
                    import_data = data.get('config_data')
                    if import_data:
                        result = EnhancedSecurityConfig.import_configuration(import_data)
                        return jsonify(result)
                
                # Handle individual setting updates
                updated_count = 0
                for key, value in data.items():
                    if key.startswith(('ddos_', 'rate_limiting_', 'auto_ban_', 'geographic_',
                                     'suspicious_activity_', 'ctf_', 'alerting_', 'threat_intel_',
                                     'performance_', 'logging_', 'container_', 'compliance_',
                                     'emergency_', 'notifications_')):
                        SecurityConfig.set_config(key, value)
                        updated_count += 1

                return jsonify({
                    'success': True,
                    'message': f'Updated {updated_count} configuration settings'
                })
                
            except Exception as e:
                return jsonify({
                    'success': False,
                    'error': f'Configuration update failed: {str(e)}'
                })

        # GET request - return configuration page
        try:
            config_summary = EnhancedSecurityConfig.get_configuration_summary()
            validation_results = EnhancedSecurityConfig.validate_configuration()
            security_profiles = {
                'permissive': EnhancedSecurityConfig.get_security_profile('permissive'),
                'balanced': EnhancedSecurityConfig.get_security_profile('balanced'),
                'strict': EnhancedSecurityConfig.get_security_profile('strict'),
                'enterprise': EnhancedSecurityConfig.get_security_profile('enterprise')
            }
            
            return render_template(
                'enhanced_security_config.html',
                config_summary=config_summary,
                validation_results=validation_results,
                security_profiles=security_profiles,
                nonce=generate_nonce()
            )
            
        except Exception as e:
            current_app.logger.error(f"Enhanced config page error: {str(e)}")
            return render_template('error.html', error="Configuration page loading failed"), 500

    @admin_blueprint.route('/events')
    @admins_only
    def enhanced_events():
        """Enhanced security events listing with filtering"""
        try:
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 50, type=int)
            event_type = request.args.get('type', '')
            severity = request.args.get('severity', '')
            source_ip = request.args.get('ip', '')
            hours = request.args.get('hours', 24, type=int)

            query = SecurityEvent.query
            
            # Apply filters
            if event_type:
                query = query.filter(SecurityEvent.event_type.like(f'%{event_type}%'))
            if severity:
                query = query.filter_by(severity=severity)
            if source_ip:
                query = query.filter_by(source_ip=source_ip)
            
            # Time filter
            since = datetime.utcnow() - timedelta(hours=hours)
            query = query.filter(SecurityEvent.timestamp >= since)

            events = query.order_by(
                SecurityEvent.timestamp.desc()
            ).paginate(
                page=page, per_page=per_page, error_out=False
            )
            
            # Get event statistics for the current filter
            event_stats = {
                'total_filtered': query.count(),
                'severity_breakdown': db.session.query(
                    SecurityEvent.severity,
                    db.func.count(SecurityEvent.id)
                ).filter(SecurityEvent.timestamp >= since).group_by(SecurityEvent.severity).all(),
                'type_breakdown': db.session.query(
                    SecurityEvent.event_type,
                    db.func.count(SecurityEvent.id)
                ).filter(SecurityEvent.timestamp >= since).group_by(SecurityEvent.event_type).limit(10).all()
            }

            return render_template(
                'enhanced_security_events.html',
                events=events,
                event_stats=event_stats,
                filters={
                    'type': event_type,
                    'severity': severity,
                    'ip': source_ip,
                    'hours': hours
                },
                nonce=generate_nonce()
            )
            
        except Exception as e:
            current_app.logger.error(f"Enhanced events page error: {str(e)}")
            return render_template('error.html', error="Events page loading failed"), 500

    @admin_blueprint.route('/alerts')
    @admins_only
    def enhanced_alerts():
        """Enhanced security alerts management with grouping"""
        try:
            status_filter = request.args.get('status', 'active')
            severity_filter = request.args.get('severity', '')
            
            query = SecurityAlert.query
            
            if status_filter and status_filter != 'all':
                query = query.filter_by(status=status_filter)
            if severity_filter:
                query = query.filter_by(severity=severity_filter)

            alerts = query.order_by(SecurityAlert.created_at.desc()).limit(100).all()
            
            # Get alert statistics
            alert_statistics = enhanced_alerting.get_alert_statistics()

            return render_template(
                'enhanced_security_alerts.html',
                alerts=alerts,
                alert_statistics=alert_statistics,
                filters={
                    'status': status_filter,
                    'severity': severity_filter
                },
                nonce=generate_nonce()
            )
            
        except Exception as e:
            current_app.logger.error(f"Enhanced alerts page error: {str(e)}")
            return render_template('error.html', error="Alerts page loading failed"), 500

    @admin_blueprint.route('/api-security')
    @admins_only
    def api_security_dashboard():
        """CTF API security dashboard"""
        if not ctf_api_security:
            return render_template('error.html', error="API security not available"), 404
            
        try:
            api_stats = ctf_api_security.get_api_security_stats()
            
            return render_template(
                'ctf_api_security.html',
                api_stats=api_stats,
                nonce=generate_nonce()
            )
            
        except Exception as e:
            current_app.logger.error(f"API security dashboard error: {str(e)}")
            return render_template('error.html', error="API security dashboard failed"), 500

    @admin_blueprint.route('/threat-intelligence')
    @admins_only
    def threat_intelligence_dashboard():
        """Threat intelligence dashboard"""
        try:
            # Get threat intelligence statistics
            threat_stats = {
                'enabled': SecurityConfig.get_config('threat_intel_enabled', False),
                'sources_configured': len([
                    s for s in SecurityConfig.get_config('threat_intel_sources', {}).values()
                    if s.get('enabled', False)
                ]),
                'reputation_cache_size': 0,  # Would need to implement cache size tracking
                'recent_lookups': 0           # Would need to implement lookup tracking
            }
            
            return render_template(
                'threat_intelligence.html',
                threat_stats=threat_stats,
                nonce=generate_nonce()
            )
            
        except Exception as e:
            current_app.logger.error(f"Threat intelligence dashboard error: {str(e)}")
            return render_template('error.html', error="Threat intelligence dashboard failed"), 500

    # Enhanced API Endpoints
    
    @security_namespace.route('/stats/enhanced')
    class EnhancedSecurityStats(Resource):
        @admins_only
        def get(self):
            """Get enhanced security statistics"""
            try:
                stats = security_monitor.get_security_stats()
                config_metrics = EnhancedSecurityConfig.get_security_metrics()
                
                # Combine statistics
                enhanced_stats = {
                    **stats,
                    'configuration_metrics': config_metrics,
                    'timestamp': datetime.utcnow().isoformat()
                }
                
                return enhanced_stats
            except Exception as e:
                return {'error': f'Failed to get enhanced stats: {str(e)}'}, 500

    @security_namespace.route('/config/export')
    class ConfigurationExport(Resource):
        @admins_only
        def get(self):
            """Export security configuration"""
            try:
                export_data = EnhancedSecurityConfig.export_configuration()
                return export_data
            except Exception as e:
                return {'error': f'Configuration export failed: {str(e)}'}, 500

    @security_namespace.route('/config/validate')
    class ConfigurationValidation(Resource):
        @admins_only
        def get(self):
            """Validate security configuration"""
            try:
                validation_results = EnhancedSecurityConfig.validate_configuration()
                return validation_results
            except Exception as e:
                return {'error': f'Configuration validation failed: {str(e)}'}, 500

    @security_namespace.route('/alerts/test-channels')
    class TestAlertChannels(Resource):
        @admins_only
        def post(self):
            """Test alert notification channels"""
            try:
                test_result = enhanced_alerting.test_alert_channels()
                return test_result
            except Exception as e:
                return {'error': f'Alert channel test failed: {str(e)}'}, 500

    @security_namespace.route('/api-security/check')
    class APISecurityCheck(Resource):
        @admins_only
        def post(self):
            """Test API security check"""
            if not ctf_api_security:
                return {'error': 'API security not available'}, 404
                
            try:
                data = request.get_json()
                endpoint = data.get('endpoint', '/api/v1/challenges')
                method = data.get('method', 'GET')
                
                # Simulate security check
                security_result = ctf_api_security.check_api_security(
                    endpoint, method, request, session.get('id')
                )
                
                return {
                    'endpoint': endpoint,
                    'method': method,
                    'security_result': security_result
                }
            except Exception as e:
                return {'error': f'API security check failed: {str(e)}'}, 500

    @security_namespace.route('/threat-intel/lookup/<ip>')
    class ThreatIntelligenceLookup(Resource):
        @admins_only
        def get(self, ip):
            """Lookup IP in threat intelligence"""
            try:
                # Placeholder for threat intelligence lookup
                # In production, this would query real threat intelligence services
                
                lookup_result = {
                    'ip': ip,
                    'reputation_score': 0,
                    'threat_categories': [],
                    'last_seen': None,
                    'sources': [],
                    'confidence': 'low'
                }
                
                return lookup_result
            except Exception as e:
                return {'error': f'Threat intelligence lookup failed: {str(e)}'}, 500

    # Enhanced Security Middleware with comprehensive protection
    
    @app.before_request
    def enhanced_security_middleware():
        """Comprehensive enhanced security monitoring middleware"""
        # Skip static assets and internal endpoints
        if (request.endpoint in ['static'] or 
            request.path.startswith(('/themes/', '/assets/', '/favicon.ico'))):
            return

        ip = get_ip()
        user_id = session.get('id')
        endpoint = request.endpoint or 'unknown'
        
        try:
            # Check if IP is banned (fast check first)
            if SecurityBan.is_banned(ip):
                enhanced_detector.log_security_event(
                    'banned_ip_access_attempt',
                    'critical',
                    ip,
                    user_id,
                    {
                        'endpoint': endpoint,
                        'method': request.method,
                        'blocked': True,
                        'user_agent': request.headers.get('User-Agent', '')[:200]
                    }
                )
                return jsonify({'error': 'Access denied', 'code': 'IP_BANNED'}), 403

            # Enhanced DDoS detection
            if hasattr(enhanced_detector, 'detect_advanced_ddos'):
                user_agent = request.headers.get('User-Agent', '')
                if enhanced_detector.detect_advanced_ddos(ip, endpoint, user_agent):
                    return jsonify({'error': 'DDoS detected', 'code': 'DDOS_BLOCKED'}), 429

            # Enhanced rate limiting
            if hasattr(enhanced_detector, 'check_advanced_rate_limit'):
                content_length = request.headers.get('Content-Length')
                request_size = int(content_length) if content_length and content_length.isdigit() else None
                
                if enhanced_detector.check_advanced_rate_limit(ip, endpoint, user_agent, request_size):
                    return jsonify({'error': 'Rate limit exceeded', 'code': 'RATE_LIMITED'}), 429

            # CTF API security checks
            if ctf_api_security and request.path.startswith('/api/'):
                security_result = ctf_api_security.check_api_security(
                    request.path, request.method, request, user_id
                )
                
                if not security_result['allowed']:
                    # Log blocked API request
                    enhanced_detector.log_security_event(
                        'api_request_blocked',
                        'high',
                        ip,
                        user_id,
                        {
                            'endpoint': request.path,
                            'method': request.method,
                            'violations': security_result['violations'],
                            'threat_level': security_result['threat_level']
                        }
                    )
                    return jsonify({
                        'error': 'API security violation',
                        'code': 'API_BLOCKED'
                    }), 403

            # Enhanced suspicious activity detection
            if hasattr(enhanced_detector, 'detect_advanced_suspicious_activity'):
                if enhanced_detector.detect_advanced_suspicious_activity(ip, endpoint, request):
                    # Check if blocking is enabled
                    if SecurityConfig.get_config('suspicious_activity_block_enabled', False):
                        return jsonify({
                            'error': 'Suspicious activity detected',
                            'code': 'SUSPICIOUS_BLOCKED'
                        }), 403

            # Log all requests for comprehensive monitoring (with sampling to reduce load)
            if SecurityConfig.get_config('logging_enhanced_enabled', True):
                # Sample requests to avoid overwhelming the system
                if hash(f"{ip}{endpoint}{time.time():.0f}") % 10 == 0:  # 10% sampling
                    enhanced_detector.log_security_event(
                        'http_request_sampled',
                        'info',
                        ip,
                        user_id,
                        {
                            'endpoint': endpoint,
                            'method': request.method,
                            'path': request.path,
                            'user_agent': request.headers.get('User-Agent', '')[:200],
                            'sampled': True
                        }
                    )

        except Exception as e:
            # Don't let security middleware break the application
            current_app.logger.error(f"Enhanced security middleware error: {str(e)}")

    @app.after_request
    def enhanced_response_monitoring(response):
        """Enhanced response monitoring for security events"""
        # Skip for static assets
        if (request.endpoint in ['static'] or 
            request.path.startswith(('/themes/', '/assets/', '/favicon.ico'))):
            return response

        ip = get_ip()
        user_id = session.get('id')

        try:
            # Enhanced authentication event logging
            if request.endpoint == 'auth.login' and request.method == 'POST':
                if response.status_code == 200 and user_id:
                    enhanced_detector.log_security_event(
                        'login_success_enhanced',
                        'info',
                        ip,
                        user_id,
                        {
                            'endpoint': 'auth.login',
                            'user_agent': request.headers.get('User-Agent', '')[:200],
                            'session_id': session.get('session_id', 'unknown')[:16]
                        }
                    )
                elif response.status_code in [401, 403]:
                    enhanced_detector.log_security_event(
                        'login_failure_enhanced',
                        'medium',
                        ip,
                        None,
                        {
                            'endpoint': 'auth.login',
                            'attempted_user': request.form.get('name', '')[:50],
                            'status_code': response.status_code,
                            'user_agent': request.headers.get('User-Agent', '')[:100]
                        }
                    )

            # Enhanced flag submission monitoring
            elif ('attempt' in str(request.endpoint) or 
                  request.path.endswith('/attempt')) and request.method == 'POST':
                
                try:
                    # Extract challenge ID
                    challenge_id = None
                    if request.is_json:
                        data = request.get_json()
                        challenge_id = data.get('challenge_id') if data else None
                    
                    # Determine result from response
                    event_type = 'flag_submission_unknown'
                    severity = 'medium'
                    
                    if response.status_code == 200:
                        try:
                            response_data = response.get_json() if hasattr(response, 'get_json') else {}
                            status = response_data.get('data', {}).get('status', 'unknown')
                            
                            if status == 'correct':
                                event_type, severity = 'flag_submission_success_enhanced', 'low'
                            elif status == 'incorrect':
                                event_type, severity = 'flag_submission_failure_enhanced', 'low'
                            elif status == 'ratelimited':
                                event_type, severity = 'flag_submission_ratelimited_enhanced', 'high'
                            else:
                                event_type, severity = 'flag_submission_unknown_enhanced', 'medium'
                        except:
                            event_type, severity = 'flag_submission_response_error', 'medium'
                    elif response.status_code == 429:
                        event_type, severity = 'flag_submission_ratelimited_enhanced', 'high'
                    elif response.status_code >= 400:
                        event_type, severity = 'flag_submission_error_enhanced', 'medium'

                    enhanced_detector.log_security_event(
                        event_type,
                        severity,
                        ip,
                        user_id,
                        {
                            'challenge_id': challenge_id,
                            'status_code': response.status_code,
                            'endpoint': request.path,
                            'method': request.method,
                            'response_size': len(response.data) if hasattr(response, 'data') else 0
                        }
                    )

                except Exception as flag_error:
                    current_app.logger.error(f"Flag submission monitoring error: {str(flag_error)}")

        except Exception as e:
            # Don't let response monitoring break the application
            current_app.logger.error(f"Enhanced response monitoring error: {str(e)}")

        return response

    # Register blueprints and API
    app.register_blueprint(admin_blueprint)
    CTFd_API_v1.add_namespace(security_namespace, path="/plugins/security_monitor")

    # Register enhanced admin menu
    register_admin_plugin_menu_bar(
        "🛡️ Enhanced Security",
        "/plugins/security_monitor/admin/"
    )

    # Register metrics endpoint for Prometheus
    try:
        register_metrics_endpoint(app)
        print(f" * Prometheus metrics endpoint registered")
    except Exception as e:
        print(f" * WARNING: Failed to register metrics endpoint: {str(e)}")

    # Perform initial system health check
    try:
        security_monitor.check_system_health()
        print(f" * Initial system health check completed")
    except Exception as e:
        print(f" * WARNING: System health check failed: {str(e)}")

    print(f" * Enhanced Security Monitor v2.0.0 loaded successfully")
    print(f" * Features: Advanced DDoS protection, ML-based detection, CTF API security")
    print(f" * Dashboard: /plugins/security_monitor/admin/")
    
    return True
