"""
Data collection service for enhanced monitoring
Collects metrics from various sources for visualization
"""

import json
import time
import threading
import requests
from datetime import datetime, timedelta
from flask import current_app
from CTFd.models import db
from .analytics import analytics_collector
from ..models import PerformanceMetrics


class DataCollectionService:
    """Service for collecting monitoring data from various sources"""

    def __init__(self):
        self.collection_interval = 60  # seconds
        self.running = False
        self.thread = None

    def start(self):
        """Start the data collection service"""
        if not self.running:
            self.running = True
            self.thread = threading.Thread(target=self._collection_loop, daemon=True)
            self.thread.start()
            current_app.logger.info("[DataCollector] Started data collection service")

    def stop(self):
        """Stop the data collection service"""
        self.running = False
        if self.thread:
            self.thread.join()
        current_app.logger.info("[DataCollector] Stopped data collection service")

    def _collection_loop(self):
        """Main collection loop"""
        while self.running:
            try:
                self.collect_all_metrics()
                time.sleep(self.collection_interval)
            except Exception as e:
                current_app.logger.error(f"[DataCollector] Error in collection loop: {str(e)}")
                time.sleep(30)  # Wait before retrying

    def collect_all_metrics(self):
        """Collect all available metrics"""
        try:
            # Run analytics collection
            analytics_collector.run_full_collection()

            # Collect service health metrics
            self.collect_service_health()

            # Collect Redis metrics
            self.collect_redis_metrics()

            # Collect Nginx metrics
            self.collect_nginx_metrics()

            # Collect Docker metrics
            self.collect_docker_metrics()

            # NEW: Enhanced data sources
            self.collect_network_metrics()
            self.collect_database_performance()
            self.collect_file_system_metrics()
            self.collect_ssl_certificate_metrics()
            self.collect_user_behavior_analytics()
            self.collect_challenge_engagement_metrics()
            self.collect_threat_intelligence()
            self.collect_application_performance()

        except Exception as e:
            current_app.logger.error(f"[DataCollector] Error collecting metrics: {str(e)}")

    def collect_service_health(self):
        """Collect health status of various services"""
        services = {
            'ctfd': 'http://localhost:8000/api/v1/statistics',
            'nginx': 'http://localhost:82',
            'grafana': 'http://localhost:3000/api/health',
            'loki': 'http://localhost:3100/ready',
            'prometheus': 'http://localhost:9090/-/ready'
        }

        for service_name, url in services.items():
            try:
                start_time = time.time()
                response = requests.get(url, timeout=5)
                response_time = (time.time() - start_time) * 1000  # ms

                # Record response time
                self.record_metric(
                    'services', f'{service_name}_response_time', response_time, 'ms'
                )

                # Record availability
                availability = 1 if response.status_code == 200 else 0
                self.record_metric(
                    'services', f'{service_name}_availability', availability, 'boolean'
                )

            except Exception as e:
                # Service is down
                self.record_metric(
                    'services', f'{service_name}_availability', 0, 'boolean'
                )
                current_app.logger.warning(f"[DataCollector] Service {service_name} health check failed: {str(e)}")

    def collect_redis_metrics(self):
        """Collect Redis performance metrics"""
        try:
            import redis
            r = redis.Redis(host='cache', port=6379, decode_responses=True)

            info = r.info()

            # Memory usage
            used_memory = info.get('used_memory', 0)
            self.record_metric('redis', 'memory_usage', used_memory, 'bytes')

            # Connected clients
            connected_clients = info.get('connected_clients', 0)
            self.record_metric('redis', 'connected_clients', connected_clients, 'count')

            # Commands processed
            total_commands = info.get('total_commands_processed', 0)
            self.record_metric('redis', 'total_commands', total_commands, 'count')

            # Hit rate
            keyspace_hits = info.get('keyspace_hits', 0)
            keyspace_misses = info.get('keyspace_misses', 0)
            total_requests = keyspace_hits + keyspace_misses
            hit_rate = (keyspace_hits / total_requests * 100) if total_requests > 0 else 0
            self.record_metric('redis', 'hit_rate', hit_rate, 'percent')

        except Exception as e:
            current_app.logger.error(f"[DataCollector] Error collecting Redis metrics: {str(e)}")

    def collect_nginx_metrics(self):
        """Collect Nginx performance metrics"""
        try:
            # Try to get nginx status (if stub_status is enabled)
            response = requests.get('http://localhost:82/nginx_status', timeout=5)
            if response.status_code == 200:
                lines = response.text.strip().split('\n')

                # Parse nginx status
                if len(lines) >= 3:
                    # Active connections
                    active_connections = int(lines[0].split(':')[1].strip())
                    self.record_metric('nginx', 'active_connections', active_connections, 'count')

                    # Server stats
                    server_stats = lines[2].split()
                    if len(server_stats) >= 3:
                        accepts = int(server_stats[0])
                        handled = int(server_stats[1])
                        requests = int(server_stats[2])

                        self.record_metric('nginx', 'total_accepts', accepts, 'count')
                        self.record_metric('nginx', 'total_handled', handled, 'count')
                        self.record_metric('nginx', 'total_requests', requests, 'count')

        except Exception as e:
            current_app.logger.debug(f"[DataCollector] Nginx metrics not available: {str(e)}")

    def collect_docker_metrics(self):
        """Collect Docker system metrics"""
        try:
            import subprocess

            # Container count
            result = subprocess.run(['docker', 'ps', '-q'], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                container_count = len([line for line in result.stdout.strip().split('\n') if line])
                self.record_metric('docker', 'running_containers', container_count, 'count')

            # Image count
            result = subprocess.run(['docker', 'images', '-q'], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                image_count = len([line for line in result.stdout.strip().split('\n') if line])
                self.record_metric('docker', 'total_images', image_count, 'count')

            # Volume count
            result = subprocess.run(['docker', 'volume', 'ls', '-q'], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                volume_count = len([line for line in result.stdout.strip().split('\n') if line])
                self.record_metric('docker', 'total_volumes', volume_count, 'count')

            # Network count
            result = subprocess.run(['docker', 'network', 'ls', '-q'], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                network_count = len([line for line in result.stdout.strip().split('\n') if line])
                self.record_metric('docker', 'total_networks', network_count, 'count')

        except Exception as e:
            current_app.logger.error(f"[DataCollector] Error collecting Docker metrics: {str(e)}")

    def collect_ctfd_specific_metrics(self):
        """Collect CTFd-specific business metrics"""
        try:
            from CTFd.models import Users, Teams, Challenges, Submissions, Solves, Fails

            # Recent activity (last hour)
            one_hour_ago = datetime.utcnow() - timedelta(hours=1)

            recent_submissions = Submissions.query.filter(
                Submissions.date >= one_hour_ago
            ).count()
            self.record_metric('ctfd', 'submissions_1h', recent_submissions, 'count')

            recent_solves = Solves.query.filter(
                Solves.date >= one_hour_ago
            ).count()
            self.record_metric('ctfd', 'solves_1h', recent_solves, 'count')

            # Competition progress
            total_possible_points = db.session.query(
                db.func.sum(Challenges.value)
            ).filter_by(state='visible').scalar() or 0

            total_earned_points = db.session.query(
                db.func.sum(Challenges.value)
            ).join(Solves).filter(Challenges.state == 'visible').scalar() or 0

            progress_percentage = (total_earned_points / total_possible_points * 100) if total_possible_points > 0 else 0
            self.record_metric('ctfd', 'competition_progress', progress_percentage, 'percent')

            # Leaderboard dynamics
            top_teams = Teams.query.limit(10).all()
            for i, team in enumerate(top_teams):
                team_score = team.score if hasattr(team, 'score') else 0
                self.record_metric('ctfd', f'leaderboard_position_{i+1}', team_score, 'points')

        except Exception as e:
            current_app.logger.error(f"[DataCollector] Error collecting CTFd metrics: {str(e)}")

    def record_metric(self, component, metric_type, value, unit=None, metadata=None):
        """Helper method to record a performance metric"""
        try:
            metric = PerformanceMetrics(
                component=component,
                metric_type=metric_type,
                metric_value=float(value),
                unit=unit,
                perf_metadata=metadata or {}  # Fixed: use perf_metadata instead of metadata
            )
            db.session.add(metric)
            db.session.commit()
        except Exception as e:
            current_app.logger.error(f"[DataCollector] Error recording metric: {str(e)}")

    def collect_network_metrics(self):
        """Collect network traffic and connection metrics"""
        try:
            import psutil

            # Network I/O statistics
            net_io = psutil.net_io_counters()
            if net_io:
                self.record_metric('network', 'bytes_sent', net_io.bytes_sent, 'bytes')
                self.record_metric('network', 'bytes_recv', net_io.bytes_recv, 'bytes')
                self.record_metric('network', 'packets_sent', net_io.packets_sent, 'count')
                self.record_metric('network', 'packets_recv', net_io.packets_recv, 'count')
                self.record_metric('network', 'errin', net_io.errin, 'count')
                self.record_metric('network', 'errout', net_io.errout, 'count')
                self.record_metric('network', 'dropin', net_io.dropin, 'count')
                self.record_metric('network', 'dropout', net_io.dropout, 'count')

            # Network connections
            connections = psutil.net_connections()
            connection_states = {}
            for conn in connections:
                state = conn.status
                connection_states[state] = connection_states.get(state, 0) + 1

            for state, count in connection_states.items():
                self.record_metric('network', f'connections_{state.lower()}', count, 'count')

            # Active network interfaces
            interfaces = psutil.net_if_stats()
            active_interfaces = sum(1 for iface, stats in interfaces.items() if stats.isup)
            self.record_metric('network', 'active_interfaces', active_interfaces, 'count')

        except Exception as e:
            current_app.logger.error(f"[DataCollector] Error collecting network metrics: {str(e)}")

    def collect_database_performance(self):
        """Collect database performance metrics"""
        try:
            from CTFd.models import db
            import time

            # Query execution time test
            start_time = time.time()
            result = db.session.execute("SELECT COUNT(*) FROM users").scalar()
            query_time = (time.time() - start_time) * 1000
            self.record_metric('database', 'query_response_time', query_time, 'ms')

            # Database connection pool status
            pool = db.engine.pool
            self.record_metric('database', 'pool_size', pool.size(), 'count')
            self.record_metric('database', 'checked_in', pool.checkedin(), 'count')
            self.record_metric('database', 'checked_out', pool.checkedout(), 'count')
            self.record_metric('database', 'overflow', pool.overflow(), 'count')

            # Table sizes (approximate)
            tables = ['users', 'teams', 'challenges', 'submissions', 'solves', 'security_events']
            for table in tables:
                try:
                    count = db.session.execute(f"SELECT COUNT(*) FROM {table}").scalar()
                    self.record_metric('database', f'table_size_{table}', count, 'rows')
                except Exception:
                    pass  # Skip if table doesn't exist

        except Exception as e:
            current_app.logger.error(f"[DataCollector] Error collecting database metrics: {str(e)}")

    def collect_file_system_metrics(self):
        """Collect file system and disk usage metrics"""
        try:
            import psutil
            import os

            # Disk usage for important paths
            paths_to_monitor = [
                ('/', 'root'),
                ('/var/log', 'logs'),
                ('/var/uploads', 'uploads'),
                ('/tmp', 'temp')
            ]

            for path, label in paths_to_monitor:
                try:
                    if os.path.exists(path):
                        usage = psutil.disk_usage(path)
                        self.record_metric('filesystem', f'{label}_total', usage.total, 'bytes')
                        self.record_metric('filesystem', f'{label}_used', usage.used, 'bytes')
                        self.record_metric('filesystem', f'{label}_free', usage.free, 'bytes')
                        self.record_metric('filesystem', f'{label}_percent',
                                         (usage.used / usage.total) * 100, 'percent')
                except Exception:
                    pass

            # Disk I/O statistics
            disk_io = psutil.disk_io_counters()
            if disk_io:
                self.record_metric('filesystem', 'read_count', disk_io.read_count, 'count')
                self.record_metric('filesystem', 'write_count', disk_io.write_count, 'count')
                self.record_metric('filesystem', 'read_bytes', disk_io.read_bytes, 'bytes')
                self.record_metric('filesystem', 'write_bytes', disk_io.write_bytes, 'bytes')
                self.record_metric('filesystem', 'read_time', disk_io.read_time, 'ms')
                self.record_metric('filesystem', 'write_time', disk_io.write_time, 'ms')

        except Exception as e:
            current_app.logger.error(f"[DataCollector] Error collecting filesystem metrics: {str(e)}")

    def collect_ssl_certificate_metrics(self):
        """Collect SSL certificate expiration and health metrics"""
        try:
            import ssl
            import socket
            from datetime import datetime

            # Check SSL certificates for key services
            ssl_endpoints = [
                ('localhost', 443, 'main_site'),
                ('localhost', 3000, 'grafana'),
            ]

            for host, port, service in ssl_endpoints:
                try:
                    context = ssl.create_default_context()
                    with socket.create_connection((host, port), timeout=5) as sock:
                        with context.wrap_socket(sock, server_hostname=host) as ssock:
                            cert = ssock.getpeercert()

                            # Parse expiration date
                            not_after = datetime.strptime(cert['notAfter'], '%b %d %H:%M:%S %Y %Z')
                            days_until_expiry = (not_after - datetime.utcnow()).days

                            self.record_metric('ssl', f'{service}_days_until_expiry',
                                             days_until_expiry, 'days')
                            self.record_metric('ssl', f'{service}_cert_valid',
                                             1 if days_until_expiry > 0 else 0, 'boolean')

                except Exception:
                    # Certificate check failed
                    self.record_metric('ssl', f'{service}_cert_valid', 0, 'boolean')

        except Exception as e:
            current_app.logger.error(f"[DataCollector] Error collecting SSL metrics: {str(e)}")

    def collect_user_behavior_analytics(self):
        """Collect advanced user behavior analytics"""
        try:
            from CTFd.models import Users, Tracking, Submissions, Solves
            from datetime import datetime, timedelta

            # Active users in different time windows
            now = datetime.utcnow()
            time_windows = [
                (timedelta(minutes=5), '5m'),
                (timedelta(minutes=15), '15m'),
                (timedelta(hours=1), '1h'),
                (timedelta(hours=24), '24h')
            ]

            for window, label in time_windows:
                since = now - window
                active_users = Tracking.query.filter(Tracking.date >= since).distinct(Tracking.user_id).count()
                self.record_metric('user_behavior', f'active_users_{label}', active_users, 'count')

            # User session patterns
            recent_sessions = Tracking.query.filter(Tracking.date >= now - timedelta(hours=24)).all()
            session_durations = {}
            user_sessions = {}

            for track in recent_sessions:
                user_id = track.user_id
                if user_id not in user_sessions:
                    user_sessions[user_id] = []
                user_sessions[user_id].append(track.date)

            # Calculate average session duration
            total_duration = 0
            session_count = 0
            for user_id, timestamps in user_sessions.items():
                if len(timestamps) > 1:
                    timestamps.sort()
                    duration = (timestamps[-1] - timestamps[0]).total_seconds() / 60  # minutes
                    total_duration += duration
                    session_count += 1

            if session_count > 0:
                avg_session_duration = total_duration / session_count
                self.record_metric('user_behavior', 'avg_session_duration', avg_session_duration, 'minutes')

            # User engagement metrics
            submission_rate = Submissions.query.filter(Submissions.date >= now - timedelta(hours=1)).count()
            solve_rate = Solves.query.filter(Solves.date >= now - timedelta(hours=1)).count()

            self.record_metric('user_behavior', 'submission_rate_1h', submission_rate, 'count')
            self.record_metric('user_behavior', 'solve_rate_1h', solve_rate, 'count')

            if submission_rate > 0:
                success_rate = (solve_rate / submission_rate) * 100
                self.record_metric('user_behavior', 'success_rate_1h', success_rate, 'percent')

        except Exception as e:
            current_app.logger.error(f"[DataCollector] Error collecting user behavior analytics: {str(e)}")

    def collect_challenge_engagement_metrics(self):
        """Collect detailed challenge engagement and difficulty metrics"""
        try:
            from CTFd.models import Challenges, Submissions, Solves, Fails
            from datetime import datetime, timedelta

            now = datetime.utcnow()
            last_24h = now - timedelta(hours=24)

            # Challenge difficulty analysis
            challenges = Challenges.query.filter_by(state='visible').all()

            for challenge in challenges:
                # Submission and solve counts
                total_submissions = Submissions.query.filter_by(challenge_id=challenge.id).count()
                total_solves = Solves.query.filter_by(challenge_id=challenge.id).count()
                recent_submissions = Submissions.query.filter(
                    Submissions.challenge_id == challenge.id,
                    Submissions.date >= last_24h
                ).count()
                recent_solves = Solves.query.filter(
                    Solves.challenge_id == challenge.id,
                    Solves.date >= last_24h
                ).count()

                # Calculate difficulty metrics
                if total_submissions > 0:
                    solve_rate = (total_solves / total_submissions) * 100
                    self.record_metric('challenge_engagement', f'challenge_{challenge.id}_solve_rate',
                                     solve_rate, 'percent')

                self.record_metric('challenge_engagement', f'challenge_{challenge.id}_submissions_24h',
                                 recent_submissions, 'count')
                self.record_metric('challenge_engagement', f'challenge_{challenge.id}_solves_24h',
                                 recent_solves, 'count')

                # Time to first solve
                first_solve = Solves.query.filter_by(challenge_id=challenge.id).order_by(Solves.date).first()
                if first_solve and hasattr(challenge, 'created_at'):
                    time_to_first_solve = (first_solve.date - challenge.created_at).total_seconds() / 60
                    self.record_metric('challenge_engagement', f'challenge_{challenge.id}_time_to_first_solve',
                                     time_to_first_solve, 'minutes')

            # Category engagement
            category_stats = db.session.query(
                Challenges.category,
                db.func.count(Submissions.id).label('submissions'),
                db.func.count(Solves.id).label('solves')
            ).outerjoin(Submissions).outerjoin(Solves).filter(
                Challenges.state == 'visible'
            ).group_by(Challenges.category).all()

            for category, submissions, solves in category_stats:
                self.record_metric('challenge_engagement', f'category_{category}_submissions',
                                 submissions or 0, 'count')
                self.record_metric('challenge_engagement', f'category_{category}_solves',
                                 solves or 0, 'count')
                if submissions and submissions > 0:
                    category_solve_rate = (solves / submissions) * 100
                    self.record_metric('challenge_engagement', f'category_{category}_solve_rate',
                                     category_solve_rate, 'percent')

        except Exception as e:
            current_app.logger.error(f"[DataCollector] Error collecting challenge engagement metrics: {str(e)}")

    def collect_threat_intelligence(self):
        """Collect threat intelligence and security indicators"""
        try:
            from ..models import SecurityEvent, SecurityBan
            from datetime import datetime, timedelta

            now = datetime.utcnow()
            last_24h = now - timedelta(hours=24)

            # Threat pattern analysis
            threat_patterns = {
                'brute_force': ['login_failure', 'auth_failure'],
                'scanning': ['404_errors', 'suspicious_paths'],
                'injection': ['sql_injection', 'xss_attempt'],
                'ddos': ['rate_limit_exceeded', 'high_request_rate']
            }

            for threat_type, event_types in threat_patterns.items():
                threat_count = SecurityEvent.query.filter(
                    SecurityEvent.event_type.in_(event_types),
                    SecurityEvent.timestamp >= last_24h
                ).count()
                self.record_metric('threat_intelligence', f'{threat_type}_events_24h', threat_count, 'count')

            # Geographic threat distribution (if IP geolocation is available)
            unique_threat_ips = SecurityEvent.query.filter(
                SecurityEvent.timestamp >= last_24h,
                SecurityEvent.severity.in_(['high', 'critical'])
            ).distinct(SecurityEvent.source_ip).count()

            self.record_metric('threat_intelligence', 'unique_threat_ips_24h', unique_threat_ips, 'count')

            # Ban effectiveness
            active_bans = SecurityBan.query.filter_by(is_active=True).count()
            recent_bans = SecurityBan.query.filter(SecurityBan.created_at >= last_24h).count()

            self.record_metric('threat_intelligence', 'active_bans', active_bans, 'count')
            self.record_metric('threat_intelligence', 'new_bans_24h', recent_bans, 'count')

            # Security event severity distribution
            severity_counts = db.session.query(
                SecurityEvent.severity,
                db.func.count(SecurityEvent.id)
            ).filter(SecurityEvent.timestamp >= last_24h).group_by(SecurityEvent.severity).all()

            for severity, count in severity_counts:
                self.record_metric('threat_intelligence', f'events_{severity}_24h', count, 'count')

        except Exception as e:
            current_app.logger.error(f"[DataCollector] Error collecting threat intelligence: {str(e)}")

    def collect_application_performance(self):
        """Collect application-level performance metrics"""
        try:
            import psutil
            import gc
            from flask import current_app

            # Memory usage details
            process = psutil.Process()
            memory_info = process.memory_info()

            self.record_metric('application', 'memory_rss', memory_info.rss, 'bytes')
            self.record_metric('application', 'memory_vms', memory_info.vms, 'bytes')

            # CPU usage
            cpu_percent = process.cpu_percent()
            self.record_metric('application', 'cpu_percent', cpu_percent, 'percent')

            # Thread count
            thread_count = process.num_threads()
            self.record_metric('application', 'thread_count', thread_count, 'count')

            # File descriptors (Unix-like systems)
            try:
                fd_count = process.num_fds()
                self.record_metric('application', 'file_descriptors', fd_count, 'count')
            except AttributeError:
                pass  # Not available on Windows

            # Python garbage collection stats
            gc_stats = gc.get_stats()
            for i, stat in enumerate(gc_stats):
                self.record_metric('application', f'gc_generation_{i}_collections',
                                 stat['collections'], 'count')
                self.record_metric('application', f'gc_generation_{i}_collected',
                                 stat['collected'], 'count')
                self.record_metric('application', f'gc_generation_{i}_uncollectable',
                                 stat['uncollectable'], 'count')

            # Flask application metrics
            if current_app:
                # Request context metrics would go here
                # This is a placeholder for Flask-specific metrics
                pass

        except Exception as e:
            current_app.logger.error(f"[DataCollector] Error collecting application performance: {str(e)}")

    def get_recent_metrics(self, component=None, hours=24):
        """Get recent metrics for analysis"""
        try:
            since = datetime.utcnow() - timedelta(hours=hours)
            query = PerformanceMetrics.query.filter(PerformanceMetrics.timestamp >= since)

            if component:
                query = query.filter(PerformanceMetrics.component == component)

            return query.order_by(PerformanceMetrics.timestamp.desc()).all()
        except Exception as e:
            current_app.logger.error(f"[DataCollector] Error getting recent metrics: {str(e)}")
            return []

    def cleanup_old_metrics(self, days=7):
        """Clean up old metrics to prevent database bloat"""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days)

            # Clean up old performance metrics
            deleted_count = PerformanceMetrics.query.filter(
                PerformanceMetrics.timestamp < cutoff_date
            ).delete()

            db.session.commit()
            current_app.logger.info(f"[DataCollector] Cleaned up {deleted_count} old performance metrics")

        except Exception as e:
            current_app.logger.error(f"[DataCollector] Error cleaning up old metrics: {str(e)}")


# Global data collection service instance
data_collector = DataCollectionService()
