@echo off
echo ========================================
echo   COMPLETE CHECKPOINT 4 RESTORATION
echo   Fresh Start - Clean Database
echo ========================================

echo [1/8] Cleaning up any remaining containers...
docker-compose down -v 2>nul
docker system prune -f 2>nul

echo [2/8] Building fresh CTFd image...
docker-compose build ctfd

echo [3/8] Starting database and cache first...
docker-compose up -d db cache
echo Waiting for database to initialize...
timeout /t 20 >nul

echo [4/8] Starting CTFd with fresh database...
docker-compose up -d ctfd
echo Waiting for CTFd to initialize...
timeout /t 30 >nul

echo [5/8] Starting monitoring services...
docker-compose up -d loki prometheus node-exporter
echo Waiting for monitoring services...
timeout /t 15 >nul

echo [6/8] Starting Grafana and log collection...
docker-compose up -d grafana promtail
echo Waiting for Grafana to start...
timeout /t 15 >nul

echo [7/8] Starting remaining services...
docker-compose up -d nginx frps frpc
echo Waiting for all services to stabilize...
timeout /t 10 >nul

echo [8/8] Final service status check...
docker-compose ps

echo.
echo ========================================
echo   CHECKPOINT 4 RESTORATION COMPLETE!
echo ========================================
echo.
echo ✅ All services should now be running:
echo   - CTFd: http://localhost:82
echo   - Grafana: http://localhost:3000 (admin/admin)
echo   - Prometheus: http://localhost:9090
echo.
echo 🔧 Next steps:
echo   1. Visit http://localhost:82 to set up CTFd
echo   2. Visit http://localhost:3000 to access monitoring
echo   3. All plugins should be working (CTFd-whale, web_desktop, security_monitor)
echo.
echo 🎉 Your CTFd platform has been restored to Checkpoint 4 state!
echo.
pause
