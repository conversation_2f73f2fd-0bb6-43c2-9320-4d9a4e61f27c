"""
Analytics collector for CTFd platform monitoring
Collects and stores various metrics for visualization in Grafana
"""

import json
import time
import subprocess
from datetime import datetime, timedelta
from collections import defaultdict
from flask import current_app, session, request
from CTFd.models import db, Users, Teams, Challenges, Submissions, Solves, Fails, Tracking
from ..models import PlatformAnalytics, ContainerMetrics, PerformanceMetrics

# Optional import for system metrics
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False


class AnalyticsCollector:
    """Collects and stores analytics data for monitoring dashboards"""

    def __init__(self):
        self.session_start_times = {}

    def record_user_activity(self, user_id, activity_type, metadata=None):
        """Record user activity metrics"""
        try:
            metric = PlatformAnalytics(
                metric_category='user_activity',
                metric_name=activity_type,
                metric_value=1,
                user_id=user_id,
                event_metadata=metadata or {}  # Fixed: use event_metadata instead of metadata
            )
            db.session.add(metric)
            db.session.commit()
        except Exception as e:
            current_app.logger.error(f"[Analytics] Error recording user activity: {str(e)}")

    def record_challenge_engagement(self, user_id, challenge_id, action, duration=None, metadata=None):
        """Record challenge engagement metrics"""
        try:
            metric_value = duration if duration else 1
            metric = PlatformAnalytics(
                metric_category='challenge_engagement',
                metric_name=action,
                metric_value=metric_value,
                user_id=user_id,
                challenge_id=challenge_id,
                event_metadata=metadata or {}  # Fixed: use event_metadata instead of metadata
            )
            db.session.add(metric)
            db.session.commit()
        except Exception as e:
            current_app.logger.error(f"[Analytics] Error recording challenge engagement: {str(e)}")

    def record_container_action(self, container_type, container_id, user_id, action,
                              challenge_id=None, duration=None, status='success',
                              resource_usage=None, error_message=None):
        """Record container lifecycle events"""
        try:
            metric = ContainerMetrics(
                container_type=container_type,
                container_id=container_id,
                user_id=user_id,
                challenge_id=challenge_id,
                action=action,
                duration=duration,
                status=status,
                resource_usage=resource_usage or {},
                error_message=error_message
            )
            db.session.add(metric)
            db.session.commit()
        except Exception as e:
            current_app.logger.error(f"[Analytics] Error recording container action: {str(e)}")

    def record_performance_metric(self, component, metric_type, value, unit=None,
                                threshold_warning=None, threshold_critical=None, metadata=None):
        """Record system performance metrics"""
        try:
            metric = PerformanceMetrics(
                component=component,
                metric_type=metric_type,
                metric_value=value,
                unit=unit,
                threshold_warning=threshold_warning,
                threshold_critical=threshold_critical,
                perf_metadata=metadata or {}  # Fixed: use perf_metadata instead of metadata
            )
            db.session.add(metric)
            db.session.commit()
        except Exception as e:
            current_app.logger.error(f"[Analytics] Error recording performance metric: {str(e)}")

    def collect_user_session_metrics(self):
        """Collect user session and activity metrics"""
        try:
            # Active users in last hour
            one_hour_ago = datetime.utcnow() - timedelta(hours=1)
            active_users = Tracking.query.filter(
                Tracking.date >= one_hour_ago
            ).distinct(Tracking.user_id).count()

            self.record_performance_metric(
                'platform', 'active_users_1h', active_users, 'count'
            )

            # Total registered users
            total_users = Users.query.count()
            self.record_performance_metric(
                'platform', 'total_users', total_users, 'count'
            )

            # Total teams
            total_teams = Teams.query.count()
            self.record_performance_metric(
                'platform', 'total_teams', total_teams, 'count'
            )

            # Challenge statistics
            total_challenges = Challenges.query.count()
            visible_challenges = Challenges.query.filter_by(state='visible').count()

            self.record_performance_metric(
                'platform', 'total_challenges', total_challenges, 'count'
            )
            self.record_performance_metric(
                'platform', 'visible_challenges', visible_challenges, 'count'
            )

            # Submission statistics
            total_submissions = Submissions.query.count()
            total_solves = Solves.query.count()
            total_fails = Fails.query.count()

            solve_rate = (total_solves / total_submissions * 100) if total_submissions > 0 else 0

            self.record_performance_metric(
                'platform', 'total_submissions', total_submissions, 'count'
            )
            self.record_performance_metric(
                'platform', 'total_solves', total_solves, 'count'
            )
            self.record_performance_metric(
                'platform', 'total_fails', total_fails, 'count'
            )
            self.record_performance_metric(
                'platform', 'solve_rate', solve_rate, 'percent'
            )

        except Exception as e:
            current_app.logger.error(f"[Analytics] Error collecting user session metrics: {str(e)}")

    def collect_challenge_analytics(self):
        """Collect challenge-specific analytics"""
        try:
            # Challenge difficulty analysis (based on solve rates)
            challenges = Challenges.query.filter_by(state='visible').all()

            for challenge in challenges:
                total_attempts = Submissions.query.filter_by(challenge_id=challenge.id).count()
                successful_solves = Solves.query.filter_by(challenge_id=challenge.id).count()

                if total_attempts > 0:
                    difficulty_score = (1 - (successful_solves / total_attempts)) * 100

                    self.record_challenge_engagement(
                        user_id=None,
                        challenge_id=challenge.id,
                        action='difficulty_analysis',
                        duration=difficulty_score,
                        metadata={
                            'total_attempts': total_attempts,
                            'successful_solves': successful_solves,
                            'challenge_name': challenge.name,
                            'category': challenge.category
                        }
                    )

            # Popular categories
            category_stats = db.session.query(
                Challenges.category,
                db.func.count(Solves.id).label('solve_count')
            ).join(Solves).group_by(Challenges.category).all()

            for category, solve_count in category_stats:
                self.record_performance_metric(
                    'challenges', f'category_{category}_solves', solve_count, 'count'
                )

        except Exception as e:
            current_app.logger.error(f"[Analytics] Error collecting challenge analytics: {str(e)}")

    def collect_container_analytics(self):
        """Collect container usage analytics"""
        try:
            # Import whale and desktop models
            try:
                from CTFd.plugins.ctfd_whale.models import WhaleContainer
                whale_containers = WhaleContainer.query.count()
                active_whale = WhaleContainer.query.filter(
                    WhaleContainer.start_time >= datetime.utcnow() - timedelta(hours=1)
                ).count()

                self.record_performance_metric(
                    'containers', 'whale_total', whale_containers, 'count'
                )
                self.record_performance_metric(
                    'containers', 'whale_active_1h', active_whale, 'count'
                )
            except ImportError:
                pass

            try:
                from CTFd.plugins.web_desktop.models import DesktopContainer
                desktop_containers = DesktopContainer.query.count()
                active_desktop = DesktopContainer.query.filter(
                    DesktopContainer.start_time >= datetime.utcnow() - timedelta(hours=1)
                ).count()

                self.record_performance_metric(
                    'containers', 'desktop_total', desktop_containers, 'count'
                )
                self.record_performance_metric(
                    'containers', 'desktop_active_1h', active_desktop, 'count'
                )
            except ImportError:
                pass

            # Docker system metrics
            try:
                result = subprocess.run(['docker', 'system', 'df', '--format', 'json'],
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    docker_info = json.loads(result.stdout)
                    if 'Images' in docker_info:
                        for item in docker_info['Images']:
                            if 'Size' in item:
                                self.record_performance_metric(
                                    'docker', 'images_size', item['Size'], 'bytes'
                                )
            except Exception:
                pass

        except Exception as e:
            current_app.logger.error(f"[Analytics] Error collecting container analytics: {str(e)}")

    def collect_system_performance(self):
        """Collect system performance metrics"""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            self.record_performance_metric(
                'system', 'cpu_usage', cpu_percent, 'percent',
                threshold_warning=80, threshold_critical=95
            )

            # Memory usage
            memory = psutil.virtual_memory()
            self.record_performance_metric(
                'system', 'memory_usage', memory.percent, 'percent',
                threshold_warning=80, threshold_critical=95
            )
            self.record_performance_metric(
                'system', 'memory_available', memory.available / (1024**3), 'GB'
            )

            # Disk usage
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            self.record_performance_metric(
                'system', 'disk_usage', disk_percent, 'percent',
                threshold_warning=80, threshold_critical=95
            )

            # Network I/O
            network = psutil.net_io_counters()
            self.record_performance_metric(
                'system', 'network_bytes_sent', network.bytes_sent, 'bytes'
            )
            self.record_performance_metric(
                'system', 'network_bytes_recv', network.bytes_recv, 'bytes'
            )

        except Exception as e:
            current_app.logger.error(f"[Analytics] Error collecting system performance: {str(e)}")

    def collect_database_metrics(self):
        """Collect database performance metrics"""
        try:
            # Database connection count
            result = db.session.execute("SHOW STATUS LIKE 'Threads_connected'")
            connections = result.fetchone()
            if connections:
                self.record_performance_metric(
                    'database', 'connections', float(connections[1]), 'count'
                )

            # Query cache hit rate
            result = db.session.execute("SHOW STATUS LIKE 'Qcache_hits'")
            hits = result.fetchone()
            result = db.session.execute("SHOW STATUS LIKE 'Qcache_inserts'")
            inserts = result.fetchone()

            if hits and inserts:
                total_queries = float(hits[1]) + float(inserts[1])
                hit_rate = (float(hits[1]) / total_queries * 100) if total_queries > 0 else 0
                self.record_performance_metric(
                    'database', 'query_cache_hit_rate', hit_rate, 'percent'
                )

        except Exception as e:
            current_app.logger.error(f"[Analytics] Error collecting database metrics: {str(e)}")

    def run_full_collection(self):
        """Run all analytics collection methods"""
        current_app.logger.info("[Analytics] Starting full analytics collection")

        self.collect_user_session_metrics()
        self.collect_challenge_analytics()
        self.collect_container_analytics()
        self.collect_system_performance()
        self.collect_database_metrics()

        current_app.logger.info("[Analytics] Completed full analytics collection")


# Global analytics collector instance
analytics_collector = AnalyticsCollector()
