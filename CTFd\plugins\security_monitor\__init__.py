import os
import json
import time
import logging
from datetime import datetime, timedelta
from flask import Blueprint, render_template, request, jsonify, current_app, session, abort
from flask_restx import Namespace, Resource

from CTFd.models import db, Users, Challenges, Submissions, Fails
from CTFd.plugins import (
    register_plugin_assets_directory,
    register_admin_plugin_menu_bar,
    bypass_csrf_protection
)
from CTFd.utils.decorators import authed_only, admins_only
from CTFd.utils import get_config, set_config, user as current_user
from CTFd.utils.user import get_ip
from CTFd.utils.security.csrf import generate_nonce
from CTFd.api import CTFd_API_v1
from CTFd.cache import cache

from .models import SecurityEvent, SecurityConfig, SecurityAlert, SecurityBan, SecurityMetrics, PlatformAnalytics, ContainerMetrics, PerformanceMetrics, create_all
from .utils.detector import SecurityDetector
from .utils.monitor import SecurityMonitor
from .utils.alerting import AlertManager
from .utils.analytics import analytics_collector
from .utils.data_collector import data_collector

def load(app):
    """Load the security monitoring plugin"""
    plugin_name = __name__.split('.')[-1]

    # Initialize database tables
    with app.app_context():
        create_all()

    # Initialize security components
    app.security_detector = SecurityDetector()
    app.security_monitor = SecurityMonitor()
    app.alert_manager = AlertManager()
    app.analytics_collector = analytics_collector
    app.data_collector = data_collector

    # Create local references for use in routes
    security_detector = app.security_detector
    security_monitor = app.security_monitor
    alert_manager = app.alert_manager

    # Start data collection service
    data_collector.start()

    # Register plugin assets
    register_plugin_assets_directory(
        app, base_path=f"/plugins/{plugin_name}/assets",
        endpoint=f'plugins.{plugin_name}.assets'
    )

    # Create blueprint for admin interface
    admin_blueprint = Blueprint(
        "security_monitor_admin",
        __name__,
        template_folder="templates",
        static_folder="assets",
        url_prefix="/plugins/security_monitor/admin"
    )

    # Create API namespace
    security_namespace = Namespace(
        "security_monitor",
        description="Security Monitoring API",
        path="/plugins/security_monitor"
    )

    @admin_blueprint.route('/')
    @admin_blueprint.route('/dashboard')
    @admins_only
    def dashboard():
        """Security monitoring dashboard"""
        # Get recent security events
        recent_events = SecurityEvent.query.order_by(
            SecurityEvent.timestamp.desc()
        ).limit(50).all()

        # Get security statistics
        stats = security_monitor.get_security_stats()

        # Get active alerts
        active_alerts = SecurityAlert.query.filter_by(
            status='active'
        ).order_by(SecurityAlert.created_at.desc()).all()

        return render_template(
            'security_dashboard.html',
            events=recent_events,
            stats=stats,
            alerts=active_alerts,
            nonce=generate_nonce()
        )

    @admin_blueprint.route('/config', methods=['GET', 'POST'])
    @admins_only
    def config():
        """Security configuration panel"""
        if request.method == 'POST':
            data = request.get_json() or request.form

            # Update security configuration
            for key, value in data.items():
                if key.startswith('security_'):
                    SecurityConfig.set_config(key, value)

            return jsonify({'success': True, 'message': 'Configuration updated'})

        # Get current configuration
        config_data = SecurityConfig.get_all_configs()
        return render_template('security_config.html', config=config_data, nonce=generate_nonce())

    @admin_blueprint.route('/events')
    @admins_only
    def events():
        """Security events listing"""
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        event_type = request.args.get('type', '')

        query = SecurityEvent.query
        if event_type:
            query = query.filter_by(event_type=event_type)

        events = query.order_by(
            SecurityEvent.timestamp.desc()
        ).paginate(
            page=page, per_page=per_page, error_out=False
        )

        return render_template('security_events.html', events=events, nonce=generate_nonce())

    @admin_blueprint.route('/alerts')
    @admins_only
    def alerts():
        """Security alerts management"""
        alerts = SecurityAlert.query.order_by(
            SecurityAlert.created_at.desc()
        ).all()

        return render_template('security_alerts.html', alerts=alerts, nonce=generate_nonce())

    # Register blueprints and API
    app.register_blueprint(admin_blueprint)
    CTFd_API_v1.add_namespace(security_namespace, path="/plugins/security_monitor")

    # Register admin menu
    register_admin_plugin_menu_bar(
        title="Security Monitor",
        route="/plugins/security_monitor/admin/"
    )

    # Monitor real CTFd events efficiently
    @app.after_request
    def monitor_ctfd_events(response):
        """Monitor key CTFd events"""
        try:
            ip = get_ip()
            user_id = session.get('id')
            detector = current_app.security_detector

            # Authentication events
            if request.endpoint == 'auth.login' and request.method == 'POST':
                if response.status_code == 200 and user_id:
                    detector.log_security_event('login_success', 'info', ip, user_id,
                        {'endpoint': 'auth.login', 'user_agent': request.headers.get('User-Agent', '')[:100]})
                elif response.status_code in [401, 403]:
                    detector.log_security_event('login_failure', 'medium', ip, None,
                        {'endpoint': 'auth.login', 'attempted_user': request.form.get('name', '')[:50]})

            # Registration events
            elif request.endpoint == 'auth.register' and request.method == 'POST' and response.status_code == 200:
                detector.log_security_event('user_registration', 'info', ip, session.get('id'),
                    {'endpoint': 'auth.register', 'email': request.form.get('email', '')[:50]})

            # Flag submission events
            elif (request.endpoint == 'api.challenges_challenge_attempt' or
                  'attempt' in str(request.endpoint)) and request.method == 'POST':

                # Determine result from response
                if response.status_code == 200:
                    response_data = response.get_json() if hasattr(response, 'get_json') else {}
                    status = response_data.get('data', {}).get('status', 'unknown')
                    if status == 'correct':
                        event_type, severity = 'flag_submission_success', 'low'
                    elif status == 'incorrect':
                        event_type, severity = 'flag_submission_failure', 'low'
                    elif status == 'ratelimited':
                        event_type, severity = 'flag_submission_ratelimited', 'high'
                    else:
                        event_type, severity = 'flag_submission_unknown', 'medium'
                elif response.status_code == 429:
                    event_type, severity = 'flag_submission_ratelimited', 'high'
                else:
                    event_type, severity = 'flag_submission_error', 'medium'

                challenge_id = request.get_json().get('challenge_id') if request.content_type == 'application/json' else request.form.get('challenge_id')
                detector.log_security_event(event_type, severity, ip, user_id,
                    {'challenge_id': challenge_id, 'status_code': response.status_code})

        except Exception as e:
            # Silently handle monitoring errors to avoid breaking CTFd
            pass

        return response
