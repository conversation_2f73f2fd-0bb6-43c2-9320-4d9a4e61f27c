"""
Simple Dashboard Plugin for CTFd
Provides two simple dashboards for general info and security logs visualization
No complex security monitoring - just clean, simple log visualization
"""

from datetime import datetime, timedelta
from flask import Blueprint, render_template, request, jsonify, session
from sqlalchemy import func, desc, and_
from sqlalchemy.exc import SQLAlchemyError

try:
    from CTFd.models import db, Users, Submissions, Solves, Fails, Challenges, Teams
    from CTFd.plugins import register_admin_plugin_menu_bar
    from CTFd.utils.decorators import admins_only
    from CTFd.utils.user import get_ip
    IMPORTS_OK = True
except ImportError as e:
    print(f"Warning: Could not import CTFd modules: {e}")
    IMPORTS_OK = False

def load(app):
    """Load the simple dashboard plugin"""
    
    if not IMPORTS_OK:
        print("Simple Dashboard Plugin: Skipping load due to import errors")
        return
    
    # Create admin blueprint
    admin_blueprint = Blueprint(
        "simple_dashboard",
        __name__,
        template_folder="templates",
        url_prefix="/plugins/simple_dashboard"
    )
    
    @admin_blueprint.route('/general')
    @admins_only
    def general_dashboard():
        """General CTFd information dashboard"""
        
        try:
            # Get basic CTFd statistics
            total_users = Users.query.count()
            total_teams = Teams.query.count() if Teams.query.first() else 0
            total_challenges = Challenges.query.count()
            total_submissions = Submissions.query.count()
            total_solves = Solves.query.count()
            
            # Recent activity (last 24 hours)
            since_24h = datetime.utcnow() - timedelta(hours=24)
            new_users_24h = Users.query.filter(Users.created >= since_24h).count()
            submissions_24h = Submissions.query.filter(Submissions.date >= since_24h).count()
            solves_24h = Solves.query.filter(Solves.date >= since_24h).count()
            
            # Most active users (by submissions) - with safer query
            top_users = []
            try:
                top_users = db.session.query(
                    Users.name,
                    func.count(Submissions.id).label('submission_count')
                ).join(Submissions, Users.id == Submissions.user_id).group_by(
                    Users.id, Users.name
                ).order_by(desc('submission_count')).limit(10).all()
            except SQLAlchemyError:
                pass
            
            # Most attempted challenges - with safer query
            challenge_stats = []
            try:
                challenge_stats = db.session.query(
                    Challenges.name,
                    func.count(Submissions.id).label('attempt_count'),
                    func.count(Solves.id).label('solve_count')
                ).select_from(Challenges).outerjoin(
                    Submissions, Challenges.id == Submissions.challenge_id
                ).outerjoin(
                    Solves, Challenges.id == Solves.challenge_id
                ).group_by(Challenges.id, Challenges.name).order_by(
                    desc('attempt_count')
                ).limit(10).all()
            except SQLAlchemyError:
                pass
            
            # Recent submissions for activity timeline
            recent_submissions = Submissions.query.order_by(
                Submissions.date.desc()
            ).limit(20).all()
            
            stats = {
                'total_users': total_users,
                'total_teams': total_teams,
                'total_challenges': total_challenges,
                'total_submissions': total_submissions,
                'total_solves': total_solves,
                'success_rate': round((total_solves / total_submissions * 100) if total_submissions > 0 else 0, 1),
                'new_users_24h': new_users_24h,
                'submissions_24h': submissions_24h,
                'solves_24h': solves_24h,
                'top_users': top_users,
                'challenge_stats': challenge_stats,
                'recent_submissions': recent_submissions
            }
            
            return render_template('general_dashboard.html', stats=stats)
            
        except Exception as e:
            # Fallback with basic stats if there's an error
            print(f"Error in general dashboard: {e}")
            stats = {
                'total_users': Users.query.count() or 0,
                'total_teams': 0,
                'total_challenges': Challenges.query.count() or 0,
                'total_submissions': Submissions.query.count() or 0,
                'total_solves': Solves.query.count() or 0,
                'success_rate': 0,
                'new_users_24h': 0,
                'submissions_24h': 0,
                'solves_24h': 0,
                'top_users': [],
                'challenge_stats': [],
                'recent_submissions': []
            }
            return render_template('general_dashboard.html', stats=stats)
    
    @admin_blueprint.route('/security')
    @admins_only
    def security_dashboard():
        """Security logs dashboard"""
        
        try:
            # Get time range from query params (default 24 hours)
            hours = request.args.get('hours', 24, type=int)
            since = datetime.utcnow() - timedelta(hours=hours)
            
            # Failed login attempts (using Fails table as proxy)
            recent_fails = Fails.query.filter(Fails.date >= since).order_by(
                Fails.date.desc()
            ).limit(100).all()
            
            # Group fails by IP (using user_id as proxy since CTFd doesn't store IPs directly)
            ip_fails = {}
            for fail in recent_fails:
                user_key = f"user_{fail.user_id}" if fail.user_id else "unknown"
                
                if user_key not in ip_fails:
                    ip_fails[user_key] = []
                ip_fails[user_key].append(fail)
            
            # Suspicious users (more than 10 failed attempts)
            suspicious_ips = {user_key: fails for user_key, fails in ip_fails.items() if len(fails) > 10}
            
            # Recent submissions for monitoring
            recent_submissions = Submissions.query.filter(
                Submissions.date >= since
            ).order_by(Submissions.date.desc()).limit(50).all()
            
            # Submission patterns (rapid submissions might indicate automation)
            submission_timeline = {}
            for sub in recent_submissions:
                hour = sub.date.replace(minute=0, second=0, microsecond=0)
                if hour not in submission_timeline:
                    submission_timeline[hour] = 0
                submission_timeline[hour] += 1
            
            # User activity patterns - with safer query
            user_activity = []
            try:
                user_activity = db.session.query(
                    Users.name,
                    func.count(Submissions.id).label('submission_count'),
                    func.max(Submissions.date).label('last_submission')
                ).join(Submissions, Users.id == Submissions.user_id).filter(
                    Submissions.date >= since
                ).group_by(Users.id, Users.name).order_by(
                    desc('submission_count')
                ).limit(20).all()
            except SQLAlchemyError:
                pass
            
            # Challenge attempt patterns - with safer query
            challenge_attempts = []
            try:
                challenge_attempts = db.session.query(
                    Challenges.name,
                    func.count(Submissions.id).label('attempts'),
                    func.count(Solves.id).label('solves')
                ).select_from(Challenges).outerjoin(
                    Submissions, and_(
                        Challenges.id == Submissions.challenge_id,
                        Submissions.date >= since
                    )
                ).outerjoin(
                    Solves, and_(
                        Challenges.id == Solves.challenge_id,
                        Solves.date >= since
                    )
                ).group_by(Challenges.id, Challenges.name).having(
                    func.count(Submissions.id) > 0
                ).order_by(desc('attempts')).limit(15).all()
            except SQLAlchemyError:
                pass
            
            security_data = {
                'time_range': hours,
                'total_fails': len(recent_fails),
                'unique_failing_users': len(ip_fails),
                'suspicious_ips': suspicious_ips,
                'recent_fails': recent_fails[:20],  # Show only recent 20
                'submission_timeline': dict(sorted(submission_timeline.items())),
                'user_activity': user_activity,
                'challenge_attempts': challenge_attempts,
                'total_submissions': len(recent_submissions)
            }
            
            return render_template('security_dashboard.html', data=security_data)
            
        except Exception as e:
            # Fallback with basic data if there's an error
            print(f"Error in security dashboard: {e}")
            security_data = {
                'time_range': 24,
                'total_fails': 0,
                'unique_failing_users': 0,
                'suspicious_ips': {},
                'recent_fails': [],
                'submission_timeline': {},
                'user_activity': [],
                'challenge_attempts': [],
                'total_submissions': 0
            }
            return render_template('security_dashboard.html', data=security_data)
    
    @admin_blueprint.route('/api/general-stats')
    @admins_only
    def api_general_stats():
        """API endpoint for general stats (for refresh functionality)"""
        
        # Quick stats calculation
        stats = {
            'total_users': Users.query.count(),
            'total_submissions': Submissions.query.count(),
            'total_solves': Solves.query.count(),
            'submissions_24h': Submissions.query.filter(
                Submissions.date >= datetime.utcnow() - timedelta(hours=24)
            ).count(),
            'timestamp': datetime.utcnow().isoformat()
        }
        
        return jsonify(stats)
    
    @admin_blueprint.route('/api/security-stats')
    @admins_only
    def api_security_stats():
        """API endpoint for security stats"""
        
        hours = request.args.get('hours', 24, type=int)
        since = datetime.utcnow() - timedelta(hours=hours)
        
        stats = {
            'total_fails': Fails.query.filter(Fails.date >= since).count(),
            'total_submissions': Submissions.query.filter(Submissions.date >= since).count(),
            'active_users': db.session.query(Users.id).join(Submissions).filter(
                Submissions.date >= since
            ).distinct().count(),
            'timestamp': datetime.utcnow().isoformat()
        }
        
        return jsonify(stats)
    
    # Register the blueprint
    app.register_blueprint(admin_blueprint)
    
    # Register admin menu items
    register_admin_plugin_menu_bar(
        "📊 General Dashboard",
        "/plugins/simple_dashboard/general"
    )
    
    register_admin_plugin_menu_bar(
        "🔒 Security Logs",
        "/plugins/simple_dashboard/security"
    )
    
    print("Simple Dashboard Plugin loaded successfully")
