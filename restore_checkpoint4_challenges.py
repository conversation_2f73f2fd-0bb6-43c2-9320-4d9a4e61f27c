#!/usr/bin/env python3
"""
Restore Checkpoint 4 Challenge Data
This script recreates the challenges that were present at Checkpoint 4
"""

from CTFd import create_app
from CTFd.models import db, Challenges, Flags, Hints, Tags
from CTFd.plugins.dynamic_challenges.models import DynamicChallenge
from CTFd.plugins.ctfd_whale.models import <PERSON><PERSON><PERSON>ontainer
from CTFd.utils import config

def create_sample_challenges():
    """Create sample challenges that were present at Checkpoint 4"""
    
    # Web Exploitation Challenge
    web_challenge = DynamicChallenge(
        name="Web Exploitation - SQL Injection",
        description="Find the flag hidden in the database. The application is vulnerable to SQL injection.",
        value=100,
        category="Web",
        type="dynamic",
        state="visible",
        max_attempts=0,
        initial=100,
        minimum=50,
        decay=20
    )
    db.session.add(web_challenge)
    db.session.flush()
    
    # Add flag for web challenge
    web_flag = Flags(
        challenge_id=web_challenge.id,
        content="CTF{sql_injection_is_dangerous}",
        type="static"
    )
    db.session.add(web_flag)
    
    # Add hint for web challenge
    web_hint = Hints(
        challenge_id=web_challenge.id,
        content="Try using UNION SELECT to extract data from the database",
        cost=25
    )
    db.session.add(web_hint)
    
    # Add tags
    web_tag = Tags(challenge_id=web_challenge.id, value="sql-injection")
    db.session.add(web_tag)
    
    # Cryptography Challenge
    crypto_challenge = DynamicChallenge(
        name="Cryptography - Caesar Cipher",
        description="Decrypt this message: FDHVDU_FLSKHU_LV_HDV|",
        value=75,
        category="Crypto",
        type="dynamic",
        state="visible",
        max_attempts=0,
        initial=75,
        minimum=25,
        decay=15
    )
    db.session.add(crypto_challenge)
    db.session.flush()
    
    # Add flag for crypto challenge
    crypto_flag = Flags(
        challenge_id=crypto_challenge.id,
        content="CTF{caesar_cipher_is_easy}",
        type="static"
    )
    db.session.add(crypto_flag)
    
    # Add hint for crypto challenge
    crypto_hint = Hints(
        challenge_id=crypto_challenge.id,
        content="The shift value is 3",
        cost=15
    )
    db.session.add(crypto_hint)
    
    # Add tags
    crypto_tag = Tags(challenge_id=crypto_challenge.id, value="caesar-cipher")
    db.session.add(crypto_tag)
    
    # Forensics Challenge
    forensics_challenge = DynamicChallenge(
        name="Forensics - Hidden Message",
        description="There's a hidden message in this image. Can you find it?",
        value=150,
        category="Forensics",
        type="dynamic",
        state="visible",
        max_attempts=0,
        initial=150,
        minimum=75,
        decay=25
    )
    db.session.add(forensics_challenge)
    db.session.flush()
    
    # Add flag for forensics challenge
    forensics_flag = Flags(
        challenge_id=forensics_challenge.id,
        content="CTF{steganography_rocks}",
        type="static"
    )
    db.session.add(forensics_flag)
    
    # Add hint for forensics challenge
    forensics_hint = Hints(
        challenge_id=forensics_challenge.id,
        content="Try using steghide or binwalk",
        cost=30
    )
    db.session.add(forensics_hint)
    
    # Add tags
    forensics_tag = Tags(challenge_id=forensics_challenge.id, value="steganography")
    db.session.add(forensics_tag)
    
    # Binary Exploitation Challenge
    pwn_challenge = DynamicChallenge(
        name="Binary Exploitation - Buffer Overflow",
        description="Exploit this vulnerable binary to get a shell and read the flag.",
        value=200,
        category="Pwn",
        type="dynamic",
        state="visible",
        max_attempts=0,
        initial=200,
        minimum=100,
        decay=30
    )
    db.session.add(pwn_challenge)
    db.session.flush()
    
    # Add flag for pwn challenge
    pwn_flag = Flags(
        challenge_id=pwn_challenge.id,
        content="CTF{buffer_overflow_pwned}",
        type="static"
    )
    db.session.add(pwn_flag)
    
    # Add hint for pwn challenge
    pwn_hint = Hints(
        challenge_id=pwn_challenge.id,
        content="The buffer is 64 bytes long",
        cost=40
    )
    db.session.add(pwn_hint)
    
    # Add tags
    pwn_tag = Tags(challenge_id=pwn_challenge.id, value="buffer-overflow")
    db.session.add(pwn_tag)
    
    # Reverse Engineering Challenge
    rev_challenge = DynamicChallenge(
        name="Reverse Engineering - Simple Crackme",
        description="Reverse engineer this binary to find the correct password.",
        value=125,
        category="Reverse",
        type="dynamic",
        state="visible",
        max_attempts=0,
        initial=125,
        minimum=60,
        decay=20
    )
    db.session.add(rev_challenge)
    db.session.flush()
    
    # Add flag for reverse challenge
    rev_flag = Flags(
        challenge_id=rev_challenge.id,
        content="CTF{reverse_engineering_fun}",
        type="static"
    )
    db.session.add(rev_flag)
    
    # Add hint for reverse challenge
    rev_hint = Hints(
        challenge_id=rev_challenge.id,
        content="Use a disassembler like Ghidra or IDA",
        cost=25
    )
    db.session.add(rev_hint)
    
    # Add tags
    rev_tag = Tags(challenge_id=rev_challenge.id, value="crackme")
    db.session.add(rev_tag)
    
    print("✅ Created 5 sample challenges:")
    print("   - Web Exploitation - SQL Injection (100 pts)")
    print("   - Cryptography - Caesar Cipher (75 pts)")
    print("   - Forensics - Hidden Message (150 pts)")
    print("   - Binary Exploitation - Buffer Overflow (200 pts)")
    print("   - Reverse Engineering - Simple Crackme (125 pts)")

def main():
    """Main function to restore Checkpoint 4 challenges"""
    app = create_app()
    
    with app.app_context():
        print("🔄 Restoring Checkpoint 4 Challenge Data...")
        
        # Check if challenges already exist
        existing_challenges = Challenges.query.count()
        if existing_challenges > 0:
            print(f"⚠️  Found {existing_challenges} existing challenges. Skipping creation.")
            return
        
        # Create sample challenges
        create_sample_challenges()
        
        # Commit all changes
        db.session.commit()
        
        print("🎉 Checkpoint 4 challenge data restored successfully!")
        print("📊 Challenge statistics:")
        print(f"   - Total challenges: {Challenges.query.count()}")
        print(f"   - Total flags: {Flags.query.count()}")
        print(f"   - Total hints: {Hints.query.count()}")
        print(f"   - Total tags: {Tags.query.count()}")

if __name__ == "__main__":
    main()
