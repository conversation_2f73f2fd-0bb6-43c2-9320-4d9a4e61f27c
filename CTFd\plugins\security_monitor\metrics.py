"""
Simple Security Metrics Exporter for CTFd
Exports security metrics to Prometheus format
"""

import time
from datetime import datetime, timedelta
from flask import Blueprint, Response
from CTFd.models import db
from CTFd.plugins.security_monitor.models import SecurityEvent, SecurityAlert, SecurityBan

# Create metrics blueprint
metrics_bp = Blueprint('security_metrics', __name__)

def generate_prometheus_metrics():
    """Generate Prometheus format metrics"""
    metrics = []
    
    # Helper function to add metric
    def add_metric(name, value, help_text, labels=None):
        if labels:
            label_str = ','.join([f'{k}="{v}"' for k, v in labels.items()])
            metrics.append(f'# HELP {name} {help_text}')
            metrics.append(f'# TYPE {name} gauge')
            metrics.append(f'{name}{{{label_str}}} {value}')
        else:
            metrics.append(f'# HELP {name} {help_text}')
            metrics.append(f'# TYPE {name} gauge')
            metrics.append(f'{name} {value}')
    
    try:
        # Critical events in last 5 minutes
        critical_events = SecurityEvent.query.filter(
            SecurityEvent.severity == 'critical',
            SecurityEvent.timestamp >= datetime.utcnow() - timedelta(minutes=5)
        ).count()
        add_metric('ctfd_security_events_critical', critical_events, 'Critical security events in last 5 minutes')
        
        # DDoS events in last 5 minutes
        ddos_events = SecurityEvent.query.filter(
            SecurityEvent.event_type == 'ddos_detected',
            SecurityEvent.timestamp >= datetime.utcnow() - timedelta(minutes=5)
        ).count()
        add_metric('ctfd_security_events_ddos', ddos_events, 'DDoS events detected in last 5 minutes')
        
        # Failed logins in last 5 minutes
        failed_logins = SecurityEvent.query.filter(
            SecurityEvent.event_type == 'login_failure',
            SecurityEvent.timestamp >= datetime.utcnow() - timedelta(minutes=5)
        ).count()
        add_metric('ctfd_failed_logins_5min', failed_logins, 'Failed login attempts in last 5 minutes')
        
        # Rate limited flag submissions in last 5 minutes
        ratelimited_flags = SecurityEvent.query.filter(
            SecurityEvent.event_type == 'flag_submission_ratelimited',
            SecurityEvent.timestamp >= datetime.utcnow() - timedelta(minutes=5)
        ).count()
        add_metric('ctfd_flag_ratelimited_5min', ratelimited_flags, 'Rate limited flag submissions in last 5 minutes')
        
        # Suspicious activity in last 10 minutes
        suspicious_activity = SecurityEvent.query.filter(
            SecurityEvent.event_type == 'suspicious_activity',
            SecurityEvent.timestamp >= datetime.utcnow() - timedelta(minutes=10)
        ).count()
        add_metric('ctfd_suspicious_activity_10min', suspicious_activity, 'Suspicious activities in last 10 minutes')
        
        # Total security events in last minute
        total_events_1min = SecurityEvent.query.filter(
            SecurityEvent.timestamp >= datetime.utcnow() - timedelta(minutes=1)
        ).count()
        add_metric('ctfd_security_events_1min', total_events_1min, 'Total security events in last minute')
        
        # New bans in last 5 minutes
        new_bans = SecurityBan.query.filter(
            SecurityBan.created_at >= datetime.utcnow() - timedelta(minutes=5)
        ).count()
        add_metric('ctfd_new_bans_5min', new_bans, 'New IP bans in last 5 minutes')
        
        # Active bans
        active_bans = SecurityBan.query.filter(SecurityBan.is_active == True).count()
        add_metric('ctfd_active_bans', active_bans, 'Currently active IP bans')
        
        # Active alerts
        active_alerts = SecurityAlert.query.filter(SecurityAlert.status == 'active').count()
        add_metric('ctfd_active_alerts', active_alerts, 'Currently active security alerts')
        
        # Event counts by type in last hour
        one_hour_ago = datetime.utcnow() - timedelta(hours=1)
        event_types = db.session.query(
            SecurityEvent.event_type,
            db.func.count(SecurityEvent.id).label('count')
        ).filter(
            SecurityEvent.timestamp >= one_hour_ago
        ).group_by(SecurityEvent.event_type).all()
        
        for event_type, count in event_types:
            add_metric('ctfd_security_events_by_type', count, 'Security events by type in last hour', 
                      {'event_type': event_type})
        
        # Events by severity in last hour
        severities = db.session.query(
            SecurityEvent.severity,
            db.func.count(SecurityEvent.id).label('count')
        ).filter(
            SecurityEvent.timestamp >= one_hour_ago
        ).group_by(SecurityEvent.severity).all()
        
        for severity, count in severities:
            add_metric('ctfd_security_events_by_severity', count, 'Security events by severity in last hour', 
                      {'severity': severity})
        
    except Exception as e:
        # Add error metric if database query fails
        add_metric('ctfd_metrics_error', 1, 'Error collecting security metrics')
        metrics.append(f'# Error: {str(e)}')
    
    return '\n'.join(metrics)

@metrics_bp.route('/metrics')
def prometheus_metrics():
    """Endpoint for Prometheus to scrape metrics"""
    metrics_output = generate_prometheus_metrics()
    return Response(metrics_output, mimetype='text/plain')

def register_metrics_endpoint(app):
    """Register the metrics endpoint with Flask app"""
    app.register_blueprint(metrics_bp)