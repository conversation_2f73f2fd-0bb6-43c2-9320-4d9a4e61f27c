{% extends "admin/base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">🔒 Security Logs Dashboard</h3>
                    <div class="card-tools">
                        <div class="btn-group">
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="changeTimeRange(1)">1h</button>
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="changeTimeRange(6)">6h</button>
                            <button type="button" class="btn btn-sm btn-primary" onclick="changeTimeRange(24)">24h</button>
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="changeTimeRange(168)">7d</button>
                        </div>
                        <button type="button" class="btn btn-success btn-sm ml-2" onclick="refreshSecurityData()">
                            <i class="fas fa-sync"></i> Refresh
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Security Overview -->
                    <div class="row">
                        <div class="col-lg-3 col-6">
                            <div class="small-box bg-info">
                                <div class="inner">
                                    <h3>{{ data.total_submissions }}</h3>
                                    <p>Total Submissions ({{ data.time_range }}h)</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-paper-plane"></i>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-3 col-6">
                            <div class="small-box bg-{{ 'danger' if data.total_fails > 50 else 'warning' if data.total_fails > 10 else 'success' }}">
                                <div class="inner">
                                    <h3>{{ data.total_fails }}</h3>
                                    <p>Failed Attempts</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-3 col-6">
                            <div class="small-box bg-warning">
                                <div class="inner">
                                    <h3>{{ data.unique_failing_users }}</h3>
                                    <p>Unique Users with Fails</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-users"></i>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-3 col-6">
                            <div class="small-box bg-{{ 'danger' if data.suspicious_ips|length > 0 else 'success' }}">
                                <div class="inner">
                                    <h3>{{ data.suspicious_ips|length }}</h3>
                                    <p>Suspicious Activity</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Activity Timeline -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h4>Submission Activity Timeline</h4>
                                </div>
                                <div class="card-body">
                                    {% if data.submission_timeline and data.submission_timeline.values() %}
                                    <div class="timeline-chart">
                                        {% for hour, count in data.submission_timeline.items() %}
                                        <div class="timeline-bar">
                                            <div class="bar-label">{{ hour.strftime('%H:%M') if hour else 'N/A' }}</div>
                                            <div class="bar" style="height: {{ (count / ([data.submission_timeline.values()|max, 1]|max) * 100)|int }}px;">
                                                <span class="bar-value">{{ count }}</span>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                    {% else %}
                                    <p class="text-muted">No submission activity in the selected time range.</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recent Failed Attempts -->
                    <div class="row">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h4>Recent Failed Attempts</h4>
                                </div>
                                <div class="card-body">
                                    {% if data.recent_fails %}
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Time</th>
                                                <th>User</th>
                                                <th>Challenge</th>
                                                <th>Type</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for fail in data.recent_fails %}
                                            <tr>
                                                <td>{{ fail.date.strftime('%H:%M:%S') if fail.date else 'Unknown' }}</td>
                                                <td>
                                                    {% if fail.user %}
                                                    {{ fail.user.name }}
                                                    {% else %}
                                                    <span class="text-muted">Unknown</span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    {% if fail.challenge %}
                                                    {{ fail.challenge.name }}
                                                    {% else %}
                                                    <span class="text-muted">Unknown</span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    <span class="badge badge-danger">{{ fail.type or 'Fail' }}</span>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                    {% else %}
                                    <p class="text-muted">No recent failed attempts.</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h4>Most Active Users</h4>
                                </div>
                                <div class="card-body">
                                    {% if data.user_activity %}
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>User</th>
                                                <th>Activity</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for user in data.user_activity[:10] %}
                                            <tr>
                                                <td>{{ user.name }}</td>
                                                <td>
                                                    <span class="badge badge-{{ 'warning' if user.submission_count > 100 else 'info' }}">
                                                        {{ user.submission_count }}
                                                    </span>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                    {% else %}
                                    <p class="text-muted">No user activity data.</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Suspicious Activity Alert -->
                    {% if data.suspicious_ips %}
                    <div class="row">
                        <div class="col-12">
                            <div class="alert alert-warning">
                                <h5><i class="fas fa-exclamation-triangle"></i> Suspicious Activity Detected</h5>
                                <p>The following users have high numbers of failed attempts (>10):</p>
                                <ul>
                                    {% for user_id, fails in data.suspicious_ips.items() %}
                                    <li>
                                        <strong>User ID {{ user_id }}</strong>: {{ fails|length }} failed attempts
                                        <small class="text-muted">(Last attempt: {{ fails[0].date.strftime('%Y-%m-%d %H:%M:%S') }})</small>
                                    </li>
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- Challenge Attack Patterns -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h4>Challenge Attempt Patterns</h4>
                                </div>
                                <div class="card-body">
                                    {% if data.challenge_attempts %}
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Challenge</th>
                                                <th>Total Attempts</th>
                                                <th>Successful Solves</th>
                                                <th>Success Rate</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for challenge in data.challenge_attempts %}
                                            {% set success_rate = (challenge.solves / challenge.attempts * 100) if challenge.attempts > 0 else 0 %}
                                            <tr>
                                                <td>{{ challenge.name }}</td>
                                                <td><span class="badge badge-info">{{ challenge.attempts }}</span></td>
                                                <td><span class="badge badge-success">{{ challenge.solves }}</span></td>
                                                <td>{{ "%.1f"|format(success_rate) }}%</td>
                                                <td>
                                                    {% if success_rate < 10 and challenge.attempts > 20 %}
                                                    <span class="badge badge-warning">High Difficulty</span>
                                                    {% elif challenge.attempts > 100 %}
                                                    <span class="badge badge-danger">Heavy Traffic</span>
                                                    {% else %}
                                                    <span class="badge badge-success">Normal</span>
                                                    {% endif %}
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                    {% else %}
                                    <p class="text-muted">No challenge attempt data available.</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function changeTimeRange(hours) {
    const url = new URL(window.location.href);
    url.searchParams.set('hours', hours);
    window.location.href = url.toString();
}

function refreshSecurityData() {
    const btn = document.querySelector('[onclick="refreshSecurityData()"]');
    btn.innerHTML = '<i class="fas fa-spin fa-spinner"></i> Refreshing...';
    btn.disabled = true;
    
    const currentHours = new URLSearchParams(window.location.search).get('hours') || '24';
    
    fetch(`/plugins/simple_dashboard/api/security-stats?hours=${currentHours}`)
        .then(response => response.json())
        .then(data => {
            // Show success message
            const alert = document.createElement('div');
            alert.className = 'alert alert-success alert-dismissible fade show';
            alert.innerHTML = `
                <strong>Success!</strong> Security data refreshed at ${new Date(data.timestamp).toLocaleTimeString()}
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
            `;
            document.querySelector('.card-body').insertBefore(alert, document.querySelector('.row'));
            
            // Auto-dismiss after 3 seconds
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 3000);
        })
        .catch(error => {
            console.error('Error refreshing security data:', error);
            alert('Failed to refresh security data');
        })
        .finally(() => {
            btn.innerHTML = '<i class="fas fa-sync"></i> Refresh';
            btn.disabled = false;
        });
}

// Auto-refresh every 2 minutes for security data
setInterval(refreshSecurityData, 120000);
</script>

<style>
.timeline-chart {
    display: flex;
    align-items: end;
    justify-content: space-between;
    height: 150px;
    padding: 20px 0;
    border-bottom: 2px solid #ddd;
    overflow-x: auto;
}

.timeline-bar {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 2px;
    min-width: 40px;
}

.bar {
    background: linear-gradient(to top, #007bff, #0056b3);
    width: 30px;
    min-height: 5px;
    border-radius: 3px 3px 0 0;
    position: relative;
    display: flex;
    align-items: end;
    justify-content: center;
}

.bar-value {
    color: white;
    font-size: 10px;
    font-weight: bold;
    padding: 2px;
}

.bar-label {
    font-size: 10px;
    color: #666;
    margin-bottom: 5px;
    transform: rotate(-45deg);
    white-space: nowrap;
}

.alert ul {
    margin-bottom: 0;
}

.badge {
    font-size: 0.75em;
}

.table th {
    border-top: none;
    font-weight: 600;
}

.small-box {
    border-radius: 8px;
    color: white;
    padding: 20px;
    position: relative;
    overflow: hidden;
}

.small-box .inner h3 {
    font-size: 2.2rem;
    font-weight: bold;
    margin: 0;
}

.small-box .inner p {
    font-size: 1rem;
    margin: 5px 0 0 0;
}

.small-box .icon {
    position: absolute;
    top: auto;
    bottom: 10px;
    right: 15px;
    font-size: 70px;
    opacity: 0.15;
}

.bg-info { background-color: #17a2b8 !important; }
.bg-success { background-color: #28a745 !important; }
.bg-warning { background-color: #ffc107 !important; color: #212529 !important; }
.bg-danger { background-color: #dc3545 !important; }
</style>
{% endblock %}
