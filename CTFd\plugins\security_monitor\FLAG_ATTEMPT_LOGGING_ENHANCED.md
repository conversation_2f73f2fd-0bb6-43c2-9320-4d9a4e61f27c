# Enhanced Flag Attempt Logging System

## Overview
The security monitoring system now includes comprehensive flag attempt logging with enhanced detection, alerting, and visualization capabilities. **It integrates seamlessly with CTFd's native logging system** while providing advanced security monitoring features.

## Enhanced Features

### 1. **Improved Flag Attempt Detection**
- **Multiple Endpoint Detection**: Monitors various flag submission endpoints
  - `api.challenges_challenge_attempt`
  - Any endpoint containing 'attempt'
  - Direct path matching for `/api/v1/challenges/attempt`

- **Enhanced Data Extraction**: Captures detailed submission information
  - Challenge ID
  - Submission length (for pattern analysis)
  - Submission hash (for duplicate detection)
  - User agent and referer headers
  - Response status codes and content

### 2. **Detailed Event Classification**
- **flag_submission_success**: Correct flag submissions
- **flag_submission_failure**: Incorrect flag submissions
- **flag_submission_ratelimited**: Rate-limited attempts
- **flag_submission_forbidden**: Forbidden access attempts
- **flag_submission_error**: Other error conditions
- **flag_submission_unknown**: Unclassified responses

### 3. **Brute Force Detection**
- **IP-based Monitoring**: Tracks failed attempts per IP address
  - 20+ failures in 5 minutes → Critical alert
  - 10+ failures in 5 minutes → High alert
- **Challenge-specific Monitoring**: Tracks attempts per challenge
  - 15+ failures on same challenge in 10 minutes → High alert

### 4. **CTFd Native Logging Integration**

#### Dual Logging System
The security monitor integrates with CTFd's built-in logging system:

- **CTFd submissions.log**: Flag attempts are logged to CTFd's native submissions logger
- **Security-specific logs**: Enhanced metadata in dedicated security.log
- **Cross-reference capability**: Events can be correlated across both log sources

#### CTFd Native Log Format
```
[12/15/2023 14:30:25] SECURITY: john_doe (IP: *************) failed flag submission on challenge 5 (length: 32) [SECURITY_MONITORED]
[12/15/2023 14:30:45] SECURITY: jane_smith (IP: *********) rate-limited flag submission on challenge 3 [SECURITY_THREAT]
```

#### Log Sources Integration
- **submissions.log**: CTFd's native flag attempt logging with security tags
- **security.log**: JSON-formatted security events with detailed metadata
- **application logs**: High/critical security events logged to main app logger

### 5. **Enhanced Logging Pipeline**

#### Promtail Configuration
- **CTFd Submissions Monitoring**: Dedicated job for CTFd's native submissions.log
- **Security Log Parsing**: Separate job for JSON security events
- **Dual-source Correlation**: Ability to correlate events across log sources
- **Structured Data Extraction**: Extracts challenge_id, status_code, submission_length
- **Label-based Filtering**: Enables efficient querying by event type

#### Loki Storage
- **Multi-source Logs**: Both CTFd native and security-specific logs
- **Structured Logs**: JSON format with detailed metadata
- **Efficient Querying**: Labels for quick filtering and aggregation
- **Retention Policies**: Configurable log retention

### 5. **Grafana Dashboard Enhancements**

#### New Panels Added:
1. **Flag Submission Types** (Pie Chart)
   - Distribution of success/failure/ratelimited/forbidden attempts

2. **Failed Flag Attempts by IP** (Time Series)
   - Real-time monitoring of failed attempts per IP

3. **Top Failed Flag Attempt IPs** (Table)
   - Top 10 IPs with most failed attempts in last hour

#### Query Examples:
```logql
# All flag submissions
{log_type="flag_attempts"} |~ "flag_submission"

# Failed attempts by IP
sum by (ip) (rate({log_type="flag_attempts", event_type="flag_submission_failure"} [$__rate_interval]))

# Top attacking IPs
topk(10, sum by (ip) (count_over_time({log_type="flag_attempts", event_type="flag_submission_failure"} [1h])))
```

### 6. **Statistical Analysis**

#### SecurityMonitor Enhancements:
- **24h/7d Statistics**: Comprehensive flag attempt metrics
- **Success Rate Calculation**: Track CTF difficulty and user performance
- **Challenge-specific Stats**: Per-challenge attempt analysis
- **Top Attacking IPs**: Identify potential threats
- **Brute Force Detection**: Real-time threat identification

#### Available Metrics:
```json
{
  "flag_attempts": {
    "last_24h": {
      "total": 1250,
      "success": 180,
      "failure": 1020,
      "ratelimited": 45,
      "forbidden": 5
    },
    "success_rate_24h": 14.4,
    "top_attacking_ips": [
      ["*************", 45],
      ["*********", 32]
    ],
    "challenge_stats": {
      "1": {"total": 200, "success": 25, "failure": 175},
      "2": {"total": 150, "success": 30, "failure": 120}
    },
    "brute_force_detected": true
  }
}
```

## Security Benefits

### 1. **Threat Detection**
- **Early Warning**: Detect brute force attempts in real-time
- **Pattern Recognition**: Identify suspicious submission patterns
- **IP Reputation**: Track and ban malicious IPs

### 2. **Incident Response**
- **Automated Alerts**: Immediate notification of security events
- **Detailed Context**: Rich metadata for investigation
- **Historical Analysis**: Trend analysis and forensics

### 3. **Performance Monitoring**
- **Challenge Difficulty**: Identify overly difficult challenges
- **User Behavior**: Understand submission patterns
- **System Load**: Monitor flag validation performance

## Alert Conditions

### Critical Alerts
- 20+ failed attempts from single IP in 5 minutes
- Critical security events (auto-generated)

### High Alerts
- 10+ failed attempts from single IP in 5 minutes
- 15+ failed attempts on same challenge in 10 minutes
- Forbidden access attempts

### Medium Alerts
- Individual failed flag submissions
- Unknown response types

## Configuration Options

### SecurityConfig Settings
- `security_flag_monitoring_enabled`: Enable/disable flag monitoring
- `security_flag_brute_force_threshold`: Customize brute force thresholds
- `security_flag_alert_channels`: Configure alert destinations

### Monitoring Thresholds
- **Rate Limit Threshold**: 20 failures / 5 minutes (critical)
- **Brute Force Threshold**: 10 failures / 5 minutes (high)
- **Challenge Threshold**: 15 failures / 10 minutes (high)

## Usage Examples

### Query Failed Attempts
```python
from CTFd.plugins.security_monitor.models import SecurityEvent

# Get recent failed attempts
failed_attempts = SecurityEvent.query.filter(
    SecurityEvent.event_type == 'flag_submission_failure',
    SecurityEvent.timestamp >= datetime.utcnow() - timedelta(hours=1)
).all()
```

### Generate Statistics
```python
from CTFd.plugins.security_monitor.utils.monitor import SecurityMonitor

monitor = SecurityMonitor()
stats = monitor.get_security_stats()
flag_stats = stats['flag_attempts']
```

## Troubleshooting

### Common Issues
1. **Missing Logs**: Check Promtail configuration and file paths
2. **No Alerts**: Verify SecurityConfig settings and alert channels
3. **Performance**: Monitor database query performance with high traffic

### Debug Commands
```bash
# Check log files
tail -f /var/log/CTFd/security.log

# Verify Promtail parsing
docker logs ctfd_promtail_1

# Check Grafana queries
# Access Grafana at http://localhost:3000
```

## Future Enhancements

### Planned Features
1. **Machine Learning**: Anomaly detection for submission patterns
2. **Geolocation**: IP-based geographic analysis
3. **Challenge Correlation**: Cross-challenge attempt analysis
4. **User Behavior**: Individual user pattern analysis
5. **Real-time Blocking**: Automatic IP blocking for severe threats

### Integration Opportunities
1. **External SIEM**: Export to enterprise security tools
2. **Threat Intelligence**: IP reputation feeds
3. **Automated Response**: Integration with fail2ban or iptables
4. **Notification Systems**: Slack, Discord, email integration
