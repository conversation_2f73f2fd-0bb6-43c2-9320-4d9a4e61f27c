"""
Enhanced Security Configuration Management
Centralized configuration for all advanced security features
"""

import json
from datetime import datetime, timedelta
from CTFd.models import db
from ..models import SecurityConfig

class EnhancedSecurityConfig:
    """Enhanced security configuration manager with 2025 best practices"""

    @classmethod
    def initialize_enhanced_config(cls):
        """Initialize all enhanced security configurations"""
        
        # Core Security Settings (Enhanced)
        enhanced_core_config = {
            # Enhanced DDoS Protection
            'ddos_protection_enabled': True,
            'ddos_threshold_adaptive': True,
            'ddos_base_threshold': 100,  # Reduced from 300 for better protection
            'ddos_window_seconds': 60,
            'ddos_multi_layer_detection': True,
            'ddos_behavioral_analysis': True,
            'ddos_geographic_filtering': True,
            'ddos_emergency_mode_threshold': 500,
            
            # Advanced Rate Limiting
            'rate_limiting_enhanced': True,
            'rate_limiting_progressive_penalties': True,
            'rate_limiting_user_agent_based': True,
            'rate_limiting_composite_keys': True,
            'rate_limiting_size_based': True,
            'rate_limiting_endpoint_specific': True,
            
            # Enhanced Suspicious Activity Detection
            'suspicious_activity_ml_enabled': True,
            'suspicious_activity_threat_intelligence': True,
            'suspicious_activity_behavioral_analysis': True,
            'suspicious_activity_pattern_matching_enhanced': True,
            'suspicious_activity_geographic_analysis': True,
            'suspicious_activity_timing_analysis': True,
            
            # Auto-ban and Progressive Penalties
            'auto_ban_progressive_enabled': True,
            'auto_ban_warning_threshold': 3,      # Events in 5 minutes
            'auto_ban_temp_ban_threshold': 7,     # Events in 1 hour  
            'auto_ban_permanent_threshold': 15,   # Events in 1 hour
            'auto_ban_temp_duration_hours': 6,
            'auto_ban_emergency_threshold': 25,   # Immediate permanent ban
            
            # Geographic Security
            'geographic_filtering_enabled': True,
            'high_risk_countries': ['CN', 'RU', 'KP', 'IR', 'BY', 'PK'],
            'geographic_blocking_enabled': False,  # Requires careful configuration
            'geographic_threat_scoring': True,
            'geographic_adaptive_limits': True,
        }
        
        # CTF-Specific API Security
        ctf_api_config = {
            'ctf_api_security_enabled': True,
            'ctf_flag_submission_protection': True,
            'ctf_challenge_scraping_protection': True,
            'ctf_scoreboard_manipulation_detection': True,
            'ctf_infrastructure_probing_detection': True,
            'ctf_api_fuzzing_detection': True,
            'ctf_flag_brute_force_protection': True,
            'ctf_automated_submission_detection': True,
            
            # CTF Rate Limits (per endpoint type)
            'ctf_flag_submission_rate_limit': 5,      # per 30 seconds
            'ctf_challenge_access_rate_limit': 60,    # per 60 seconds
            'ctf_scoreboard_access_rate_limit': 30,   # per 60 seconds
            'ctf_admin_api_rate_limit': 10,           # per 60 seconds
            
            # CTF Protection Thresholds
            'ctf_flag_enumeration_threshold': 50,     # attempts across challenges
            'ctf_challenge_scraping_threshold': 30,   # challenge accesses
            'ctf_brute_force_per_challenge': 20,      # attempts per challenge
            'ctf_automation_detection_samples': 10,   # samples for pattern detection
        }
        
        # Enhanced Alerting Configuration
        enhanced_alerting_config = {
            'alerting_smart_grouping_enabled': True,
            'alerting_fatigue_reduction': True,
            'alerting_threat_intelligence_enhanced': True,
            'alerting_severity_escalation': True,
            'alerting_context_enrichment': True,
            
            # Alert Grouping Rules
            'alert_grouping_ip_time_window': 300,     # 5 minutes
            'alert_grouping_pattern_time_window': 600, # 10 minutes
            'alert_grouping_severity_time_window': 900, # 15 minutes
            'alert_grouping_max_per_ip': 3,
            'alert_grouping_critical_threshold': 5,
            'alert_grouping_high_threshold': 10,
            
            # Notification Channels
            'notifications_email_enabled': False,
            'notifications_webhook_enabled': False,
            'notifications_slack_enabled': False,
            'notifications_discord_enabled': False,
            
            # Alert Cooldowns (prevent spam)
            'alert_cooldown_seconds': 300,            # 5 minutes
            'alert_cooldown_critical_seconds': 60,    # 1 minute for critical
            'alert_cooldown_per_ip_enabled': True,
        }
        
        # Threat Intelligence Configuration
        threat_intelligence_config = {
            'threat_intel_enabled': True,
            'threat_intel_ip_reputation': True,
            'threat_intel_botnet_detection': True,
            'threat_intel_tor_detection': True,
            'threat_intel_vpn_detection': True,
            'threat_intel_geo_ip_enabled': True,
            
            # Threat Intelligence Sources (placeholders - integrate with real services)
            'threat_intel_sources': {
                'abuse_ch': {'enabled': False, 'api_key': ''},
                'virustotal': {'enabled': False, 'api_key': ''},
                'abuseipdb': {'enabled': False, 'api_key': ''},
                'greynoise': {'enabled': False, 'api_key': ''},
                'shodan': {'enabled': False, 'api_key': ''}
            },
            
            # Reputation Scoring
            'reputation_scoring_enabled': True,
            'reputation_cache_duration': 3600,       # 1 hour
            'reputation_negative_threshold': -5,
            'reputation_positive_threshold': 5,
        }
        
        # Performance and Resource Management
        performance_config = {
            'performance_monitoring_enabled': True,
            'performance_resource_limits': True,
            'performance_auto_scaling': True,
            'performance_degradation_detection': True,
            
            # Resource Limits
            'max_security_events_per_minute': 1000,
            'max_concurrent_rate_limit_checks': 500,
            'max_threat_intelligence_queries_per_minute': 100,
            'security_database_query_timeout': 5,     # seconds
            
            # Performance Thresholds
            'performance_warning_threshold': 80,      # percent
            'performance_critical_threshold': 95,     # percent
            'memory_usage_warning_mb': 512,
            'cpu_usage_warning_percent': 70,
        }
        
        # Advanced Logging Configuration
        logging_config = {
            'logging_enhanced_enabled': True,
            'logging_structured_format': True,
            'logging_real_time_streaming': True,
            'logging_threat_correlation': True,
            'logging_performance_metrics': True,
            
            # Log Retention
            'log_retention_security_events_days': 90,
            'log_retention_alerts_days': 180,
            'log_retention_metrics_days': 30,
            'log_retention_debug_days': 7,
            
            # Log Levels
            'log_level_security_events': 'INFO',
            'log_level_threat_detection': 'WARNING', 
            'log_level_performance': 'INFO',
            'log_level_api_security': 'INFO',
            
            # Log Destinations
            'log_to_file_enabled': True,
            'log_to_syslog_enabled': False,
            'log_to_elasticsearch_enabled': False,
            'log_to_splunk_enabled': False,
        }
        
        # Container and Infrastructure Security
        container_security_config = {
            'container_security_monitoring': True,
            'container_resource_monitoring': True,
            'container_vulnerability_scanning': False,  # Requires external integration
            'container_runtime_protection': True,
            'container_network_monitoring': True,
            
            # Container Limits
            'max_containers_per_user': 3,
            'max_total_containers': 100,
            'container_memory_limit_mb': 512,
            'container_cpu_limit_percent': 50,
            'container_network_bandwidth_mbps': 10,
            
            # Docker Security
            'docker_socket_protection': True,
            'docker_image_scanning': False,           # Requires external tools
            'docker_security_policies': True,
        }
        
        # Compliance and Audit Configuration
        compliance_config = {
            'compliance_auditing_enabled': True,
            'compliance_gdpr_mode': False,
            'compliance_hipaa_mode': False,
            'compliance_pci_dss_mode': False,
            
            # Audit Settings
            'audit_all_security_events': True,
            'audit_admin_actions': True,
            'audit_sensitive_data_access': True,
            'audit_failed_authentication': True,
            'audit_privilege_escalation': True,
            
            # Data Protection
            'data_anonymization_enabled': False,
            'data_encryption_at_rest': False,         # Requires database-level config
            'data_encryption_in_transit': True,
            'personal_data_retention_days': 365,
        }
        
        # Emergency Response Configuration
        emergency_config = {
            'emergency_response_enabled': True,
            'emergency_lockdown_enabled': False,      # Dangerous - use carefully
            'emergency_contact_notifications': True,
            'emergency_auto_scaling': True,
            
            # Emergency Thresholds
            'emergency_attack_threshold': 1000,       # Events per minute
            'emergency_ban_threshold': 50,            # Events per IP per hour
            'emergency_resource_threshold': 95,       # Resource usage percent
            
            # Emergency Actions
            'emergency_enable_captcha': True,
            'emergency_rate_limit_all': True,
            'emergency_disable_registration': True,
            'emergency_admin_notification': True,
            
            # Recovery Settings
            'emergency_auto_recovery_enabled': True,
            'emergency_recovery_time_minutes': 30,
        }
        
        # Combine all configurations
        all_configs = {
            **enhanced_core_config,
            **ctf_api_config,
            **enhanced_alerting_config,
            **threat_intelligence_config,
            **performance_config,
            **logging_config,
            **container_security_config,
            **compliance_config,
            **emergency_config
        }
        
        # Set all configurations
        for key, value in all_configs.items():
            if not SecurityConfig.get_config(key):
                SecurityConfig.set_config(key, value)
        
        # Set initialization timestamp
        SecurityConfig.set_config('enhanced_security_initialized_at', datetime.utcnow().isoformat())
        SecurityConfig.set_config('enhanced_security_version', '2.0.0')
        
        return True

    @classmethod
    def get_security_profile(cls, profile_name='balanced'):
        """Get predefined security profiles"""
        profiles = {
            'permissive': {
                'ddos_base_threshold': 200,
                'rate_limiting_enhanced': False,
                'auto_ban_progressive_enabled': False,
                'geographic_filtering_enabled': False,
                'suspicious_activity_ml_enabled': False,
                'ctf_api_security_enabled': True,  # Always enabled for CTF
                'alerting_smart_grouping_enabled': True,
                'description': 'Minimal security for development/testing'
            },
            
            'balanced': {
                'ddos_base_threshold': 100,
                'rate_limiting_enhanced': True,
                'auto_ban_progressive_enabled': True,
                'geographic_filtering_enabled': True,
                'suspicious_activity_ml_enabled': True,
                'ctf_api_security_enabled': True,
                'alerting_smart_grouping_enabled': True,
                'description': 'Balanced security for most CTF competitions'
            },
            
            'strict': {
                'ddos_base_threshold': 50,
                'rate_limiting_enhanced': True,
                'auto_ban_progressive_enabled': True,
                'geographic_filtering_enabled': True,
                'geographic_blocking_enabled': True,
                'suspicious_activity_ml_enabled': True,
                'ctf_api_security_enabled': True,
                'alerting_smart_grouping_enabled': True,
                'threat_intel_enabled': True,
                'performance_monitoring_enabled': True,
                'description': 'Maximum security for high-stakes competitions'
            },
            
            'enterprise': {
                'ddos_base_threshold': 75,
                'rate_limiting_enhanced': True,
                'auto_ban_progressive_enabled': True,
                'geographic_filtering_enabled': True,
                'suspicious_activity_ml_enabled': True,
                'ctf_api_security_enabled': True,
                'alerting_smart_grouping_enabled': True,
                'threat_intel_enabled': True,
                'performance_monitoring_enabled': True,
                'compliance_auditing_enabled': True,
                'container_security_monitoring': True,
                'emergency_response_enabled': True,
                'description': 'Enterprise-grade security with compliance features'
            }
        }
        
        return profiles.get(profile_name, profiles['balanced'])

    @classmethod
    def apply_security_profile(cls, profile_name='balanced'):
        """Apply a predefined security profile"""
        profile = cls.get_security_profile(profile_name)
        
        for key, value in profile.items():
            if key != 'description':
                SecurityConfig.set_config(key, value)
        
        # Log profile application
        SecurityConfig.set_config('applied_security_profile', profile_name)
        SecurityConfig.set_config('security_profile_applied_at', datetime.utcnow().isoformat())
        
        return True

    @classmethod
    def get_configuration_summary(cls):
        """Get a summary of current security configuration"""
        try:
            config_summary = {
                'profile': SecurityConfig.get_config('applied_security_profile', 'unknown'),
                'version': SecurityConfig.get_config('enhanced_security_version', '1.0.0'),
                'initialized_at': SecurityConfig.get_config('enhanced_security_initialized_at'),
                'last_updated': SecurityConfig.get_config('security_profile_applied_at'),
                
                'core_features': {
                    'ddos_protection': SecurityConfig.get_config('ddos_protection_enabled', False),
                    'enhanced_rate_limiting': SecurityConfig.get_config('rate_limiting_enhanced', False),
                    'progressive_penalties': SecurityConfig.get_config('auto_ban_progressive_enabled', False),
                    'geographic_filtering': SecurityConfig.get_config('geographic_filtering_enabled', False),
                    'ml_detection': SecurityConfig.get_config('suspicious_activity_ml_enabled', False)
                },
                
                'ctf_features': {
                    'api_security': SecurityConfig.get_config('ctf_api_security_enabled', False),
                    'flag_protection': SecurityConfig.get_config('ctf_flag_submission_protection', False),
                    'scraping_protection': SecurityConfig.get_config('ctf_challenge_scraping_protection', False),
                    'brute_force_protection': SecurityConfig.get_config('ctf_flag_brute_force_protection', False)
                },
                
                'advanced_features': {
                    'smart_alerting': SecurityConfig.get_config('alerting_smart_grouping_enabled', False),
                    'threat_intelligence': SecurityConfig.get_config('threat_intel_enabled', False),
                    'performance_monitoring': SecurityConfig.get_config('performance_monitoring_enabled', False),
                    'container_security': SecurityConfig.get_config('container_security_monitoring', False),
                    'emergency_response': SecurityConfig.get_config('emergency_response_enabled', False)
                },
                
                'notification_channels': {
                    'email': SecurityConfig.get_config('notifications_email_enabled', False),
                    'webhook': SecurityConfig.get_config('notifications_webhook_enabled', False),
                    'slack': SecurityConfig.get_config('notifications_slack_enabled', False),
                    'discord': SecurityConfig.get_config('notifications_discord_enabled', False)
                }
            }
            
            return config_summary
            
        except Exception as e:
            return {'error': f'Failed to get configuration summary: {str(e)}'}

    @classmethod
    def validate_configuration(cls):
        """Validate current security configuration"""
        validation_results = {
            'valid': True,
            'warnings': [],
            'errors': [],
            'recommendations': []
        }
        
        try:
            # Check core dependencies
            if SecurityConfig.get_config('ddos_protection_enabled', False):
                if not SecurityConfig.get_config('rate_limiting_enhanced', False):
                    validation_results['warnings'].append(
                        'DDoS protection is enabled but enhanced rate limiting is disabled'
                    )
            
            # Check CTF-specific configuration
            if SecurityConfig.get_config('ctf_api_security_enabled', False):
                if not SecurityConfig.get_config('ctf_flag_submission_protection', False):
                    validation_results['warnings'].append(
                        'CTF API security is enabled but flag submission protection is disabled'
                    )
            
            # Check alert configuration
            if SecurityConfig.get_config('alerting_smart_grouping_enabled', False):
                notification_enabled = any([
                    SecurityConfig.get_config('notifications_email_enabled', False),
                    SecurityConfig.get_config('notifications_webhook_enabled', False),
                    SecurityConfig.get_config('notifications_slack_enabled', False),
                    SecurityConfig.get_config('notifications_discord_enabled', False)
                ])
                
                if not notification_enabled:
                    validation_results['warnings'].append(
                        'Alert grouping is enabled but no notification channels are configured'
                    )
            
            # Check resource limits
            max_containers = SecurityConfig.get_config('max_total_containers', 100)
            if max_containers > 200:
                validation_results['warnings'].append(
                    f'Container limit ({max_containers}) is very high and may impact performance'
                )
            
            # Check geographic filtering
            if SecurityConfig.get_config('geographic_blocking_enabled', False):
                if not SecurityConfig.get_config('geographic_filtering_enabled', False):
                    validation_results['errors'].append(
                        'Geographic blocking requires geographic filtering to be enabled'
                    )
                    validation_results['valid'] = False
            
            # Performance recommendations
            if SecurityConfig.get_config('suspicious_activity_ml_enabled', False):
                if not SecurityConfig.get_config('performance_monitoring_enabled', False):
                    validation_results['recommendations'].append(
                        'Enable performance monitoring when using ML-based detection'
                    )
            
            # Security recommendations
            if not SecurityConfig.get_config('auto_ban_progressive_enabled', False):
                validation_results['recommendations'].append(
                    'Consider enabling progressive auto-ban for better threat response'
                )
            
        except Exception as e:
            validation_results['valid'] = False
            validation_results['errors'].append(f'Configuration validation failed: {str(e)}')
        
        return validation_results

    @classmethod
    def export_configuration(cls):
        """Export current security configuration"""
        try:
            # Get all security-related configurations
            all_configs = SecurityConfig.get_all_configs()
            
            # Filter security-related configs
            security_configs = {
                k: v for k, v in all_configs.items() 
                if any(prefix in k for prefix in [
                    'ddos_', 'rate_limiting_', 'auto_ban_', 'geographic_',
                    'suspicious_activity_', 'ctf_', 'alerting_', 'threat_intel_',
                    'performance_', 'logging_', 'container_', 'compliance_',
                    'emergency_', 'notifications_', 'enhanced_security_'
                ])
            }
            
            export_data = {
                'export_timestamp': datetime.utcnow().isoformat(),
                'version': SecurityConfig.get_config('enhanced_security_version', '2.0.0'),
                'profile': SecurityConfig.get_config('applied_security_profile', 'custom'),
                'configurations': security_configs
            }
            
            return export_data
            
        except Exception as e:
            return {'error': f'Failed to export configuration: {str(e)}'}

    @classmethod
    def import_configuration(cls, import_data):
        """Import security configuration"""
        try:
            if not isinstance(import_data, dict) or 'configurations' not in import_data:
                return {'success': False, 'error': 'Invalid import data format'}
            
            imported_count = 0
            errors = []
            
            for key, value in import_data['configurations'].items():
                try:
                    SecurityConfig.set_config(key, value)
                    imported_count += 1
                except Exception as e:
                    errors.append(f'Failed to import {key}: {str(e)}')
            
            # Update import metadata
            SecurityConfig.set_config('configuration_imported_at', datetime.utcnow().isoformat())
            SecurityConfig.set_config('configuration_import_version', import_data.get('version', 'unknown'))
            
            result = {
                'success': True,
                'imported_count': imported_count,
                'total_count': len(import_data['configurations'])
            }
            
            if errors:
                result['errors'] = errors
            
            return result
            
        except Exception as e:
            return {'success': False, 'error': f'Configuration import failed: {str(e)}'}

    @classmethod
    def reset_to_defaults(cls):
        """Reset security configuration to defaults"""
        try:
            # Get current enhanced configurations
            all_configs = SecurityConfig.get_all_configs()
            security_keys = [
                k for k in all_configs.keys() 
                if any(prefix in k for prefix in [
                    'ddos_', 'rate_limiting_', 'auto_ban_', 'geographic_',
                    'suspicious_activity_', 'ctf_', 'alerting_', 'threat_intel_',
                    'performance_', 'logging_', 'container_', 'compliance_',
                    'emergency_', 'notifications_'
                ])
            ]
            
            # Remove existing configurations
            for key in security_keys:
                try:
                    # Delete configuration (implementation depends on your model)
                    config_obj = SecurityConfig.query.filter_by(key=key).first()
                    if config_obj:
                        db.session.delete(config_obj)
                except Exception:
                    pass
            
            db.session.commit()
            
            # Reinitialize with defaults
            cls.initialize_enhanced_config()
            
            # Apply balanced profile by default
            cls.apply_security_profile('balanced')
            
            return {'success': True, 'message': 'Configuration reset to defaults'}
            
        except Exception as e:
            return {'success': False, 'error': f'Failed to reset configuration: {str(e)}'}

    @classmethod
    def get_security_metrics(cls):
        """Get security configuration effectiveness metrics"""
        try:
            from ..models import SecurityEvent, SecurityAlert
            
            last_24h = datetime.utcnow() - timedelta(hours=24)
            last_7d = datetime.utcnow() - timedelta(days=7)
            
            # Basic metrics
            metrics = {
                'events_24h': SecurityEvent.query.filter(
                    SecurityEvent.timestamp >= last_24h
                ).count(),
                
                'events_7d': SecurityEvent.query.filter(
                    SecurityEvent.timestamp >= last_7d
                ).count(),
                
                'alerts_24h': SecurityAlert.query.filter(
                    SecurityAlert.created_at >= last_24h
                ).count(),
                
                'critical_events_24h': SecurityEvent.query.filter(
                    SecurityEvent.timestamp >= last_24h,
                    SecurityEvent.severity == 'critical'
                ).count(),
                
                'blocked_ips': SecurityBan.query.filter_by(is_active=True).count()
            }
            
            # Configuration effectiveness
            if SecurityConfig.get_config('ddos_protection_enabled', False):
                ddos_events = SecurityEvent.query.filter(
                    SecurityEvent.timestamp >= last_24h,
                    SecurityEvent.event_type.like('%ddos%')
                ).count()
                metrics['ddos_events_blocked'] = ddos_events
            
            if SecurityConfig.get_config('ctf_api_security_enabled', False):
                api_events = SecurityEvent.query.filter(
                    SecurityEvent.timestamp >= last_24h,
                    SecurityEvent.details.contains('"api_security": true')
                ).count()
                metrics['api_security_events'] = api_events
            
            return metrics
            
        except Exception as e:
            return {'error': f'Failed to get security metrics: {str(e)}'}
