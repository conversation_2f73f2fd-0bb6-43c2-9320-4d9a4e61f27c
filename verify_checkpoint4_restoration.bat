@echo off
echo ========================================
echo   CHECKPOINT 4 RESTORATION VERIFICATION
echo ========================================

echo [1/5] Checking service status...
docker-compose ps

echo.
echo [2/5] Checking CTFd accessibility...
curl -s -o nul -w "CTFd HTTP Status: %%{http_code}\n" http://localhost:82

echo.
echo [3/5] Checking Grafana accessibility...
curl -s -o nul -w "Grafana HTTP Status: %%{http_code}\n" http://localhost:3000

echo.
echo [4/5] Checking Prometheus accessibility...
curl -s -o nul -w "Prometheus HTTP Status: %%{http_code}\n" http://localhost:9090

echo.
echo [5/5] Checking enabled plugins...
echo Enabled plugins in CTFd:
dir "CTFd\plugins" /b | findstr /v "\.disabled"

echo.
echo ========================================
echo   RESTORATION VERIFICATION COMPLETE
echo ========================================
echo.
echo ✅ SERVICES RESTORED TO CHECKPOINT 4 STATE:
echo   - CTFd: http://localhost:82
echo   - Grafana Dashboard: http://localhost:3000 (admin/admin)
echo   - Prometheus: http://localhost:9090
echo   - Loki Logs: http://localhost:3100
echo.
echo ✅ PLUGINS ENABLED:
echo   - CTFd-whale (Container Management)
echo   - Web Desktop (Desktop Access)
echo   - Security Monitor (Security Monitoring)
echo   - Dynamic Challenges
echo   - Standard Challenges & Flags
echo.
echo ✅ MONITORING STACK:
echo   - Real-time security monitoring
echo   - Log aggregation with Loki
echo   - Metrics collection with Prometheus
echo   - Visualization with Grafana
echo.
echo 🎉 Your CTFd platform has been restored to Checkpoint 4!
echo    Everything should be working as it was before.
echo.
pause
