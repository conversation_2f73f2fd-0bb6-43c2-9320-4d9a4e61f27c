{% extends "admin/base.html" %}

{% block content %}
<div class="jumbotron">
    <div class="container">
        <h1>🛡️ Security Monitoring Dashboard</h1>
        <p>Real-time security monitoring and threat detection for your CTFd platform</p>
    </div>
</div>

<div class="container">
    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-white bg-primary">
                <div class="card-body">
                    <h5 class="card-title">Total Events (24h)</h5>
                    <h2 class="card-text">{{ stats.total_events_24h }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-warning">
                <div class="card-body">
                    <h5 class="card-title">Active Threats</h5>
                    <h2 class="card-text">{{ stats.active_threats }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-danger">
                <div class="card-body">
                    <h5 class="card-title">Blocked IPs</h5>
                    <h2 class="card-text">{{ stats.blocked_ips }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-success">
                <div class="card-body">
                    <h5 class="card-title">Security Score</h5>
                    <h2 class="card-text">{{ stats.security_score }}%</h2>
                </div>
            </div>
        </div>
    </div>

    <!-- Alerts Section -->
    {% if alerts %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4>🚨 Active Security Alerts</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Time</th>
                                    <th>Severity</th>
                                    <th>Alert Type</th>
                                    <th>Description</th>
                                    <th>Source IP</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for alert in alerts %}
                                <tr>
                                    <td>{{ alert.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                                    <td>
                                        <span class="badge badge-{{ 'danger' if alert.severity == 'critical' else 'warning' if alert.severity == 'high' else 'info' }}">
                                            {{ alert.severity }}
                                        </span>
                                    </td>
                                    <td>{{ alert.alert_type }}</td>
                                    <td>{{ alert.description }}</td>
                                    <td>{{ alert.source_ip or 'N/A' }}</td>
                                    <td>
                                        <button class="btn btn-sm btn-success" onclick="resolveAlert({{ alert.id }})">Resolve</button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Recent Events -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4>📊 Recent Security Events</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Time</th>
                                    <th>Event Type</th>
                                    <th>Severity</th>
                                    <th>Source IP</th>
                                    <th>User</th>
                                    <th>Details</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for event in events[:20] %}
                                <tr>
                                    <td>{{ event.timestamp.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                                    <td>{{ event.event_type }}</td>
                                    <td>
                                        <span class="badge badge-{{ 'danger' if event.severity == 'critical' else 'warning' if event.severity == 'high' else 'info' }}">
                                            {{ event.severity }}
                                        </span>
                                    </td>
                                    <td>{{ event.source_ip }}</td>
                                    <td>{{ event.user_id or 'Anonymous' }}</td>
                                    <td>
                                        {% if event.details %}
                                        <small>{{ event.details }}</small>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4>⚡ Quick Actions</h4>
                </div>
                <div class="card-body">
                    <a href="{{ url_for('security_monitor_admin.config') }}" class="btn btn-primary">⚙️ Security Settings</a>
                    <a href="{{ url_for('security_monitor_admin.events') }}" class="btn btn-info">📋 View All Events</a>
                    <a href="{{ url_for('security_monitor_admin.alerts') }}" class="btn btn-warning">🚨 Manage Alerts</a>
                    <button class="btn btn-danger" onclick="clearAllAlerts()">🧹 Clear Resolved Alerts</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function resolveAlert(alertId) {
    fetch(`/api/v1/plugins/security_monitor/alerts/${alertId}`, {
        method: 'PATCH',
        headers: {
            'Content-Type': 'application/json',
            'CSRF-Token': '{{ nonce }}'
        },
        body: JSON.stringify({status: 'resolved'})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        }
    });
}

function clearAllAlerts() {
    if (confirm('Are you sure you want to clear all resolved alerts?')) {
        fetch('/api/v1/plugins/security_monitor/alerts/delete-resolved', {
            method: 'DELETE',
            headers: {
                'CSRF-Token': '{{ nonce }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            }
        });
    }
}
</script>
{% endblock %}
