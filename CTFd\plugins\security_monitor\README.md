# CTFd Security Monitor Plugin

A comprehensive security monitoring and threat detection plugin for CTFd that provides real-time security monitoring, alerting, and DDoS protection.

## Features

### 🛡️ Core Security Features
- **Rate Limiting**: Configurable rate limiting per endpoint and IP
- **DDoS Detection**: Automatic detection and blocking of DDoS attacks
- **Suspicious Activity Detection**: Pattern-based detection of malicious behavior
- **IP Banning**: Automatic and manual IP banning capabilities
- **Security Event Logging**: Comprehensive logging of all security events

### 📊 Monitoring & Analytics
- **Real-time Dashboard**: Live security metrics and statistics
- **Security Events Tracking**: Detailed event logging and analysis
- **Alert Management**: Centralized alert handling and resolution
- **Threat Intelligence**: IP reputation and behavior analysis
- **Security Metrics**: Performance and security KPIs

### 🚨 Alerting System
- **Email Alerts**: SMTP-based email notifications
- **Webhook Integration**: Custom webhook notifications
- **Slack Integration**: Direct Slack channel notifications
- **Alert Severity Levels**: Critical, High, Medium, Low classifications
- **Alert Aggregation**: Intelligent alert grouping and deduplication

### 🔧 Advanced Configuration
- **Flexible Rate Limits**: Per-endpoint rate limiting configuration
- **Custom Detection Rules**: Configurable suspicious activity patterns
- **Auto-ban Policies**: Automatic IP banning based on threat scores
- **Retention Policies**: Configurable log and event retention
- **Multi-channel Alerting**: Multiple notification channels

## Installation

1. **Copy Plugin Files**
   ```bash
   cp -r security_monitor/ /path/to/CTFd/plugins/
   ```

2. **Update Docker Compose**
   Add `security_monitor` to the `PLUGIN_WHITELIST` in your `docker-compose.yml`:
   ```yaml
   environment:
     - PLUGIN_WHITELIST=web_desktop,challenges,dynamic_challenges,flags,ctfd-whale,security_monitor
   ```

3. **Enhanced Nginx Configuration**
   The plugin includes an enhanced nginx configuration with security headers and rate limiting. Update your nginx configuration or use the provided `conf/nginx/http.conf`.

4. **Restart CTFd**
   ```bash
   docker-compose down
   docker-compose up -d
   ```

## Configuration

### Basic Setup

1. **Access Admin Panel**
   - Navigate to Admin → Security Monitor
   - Go to Configuration tab

2. **Configure Rate Limiting**
   - Enable rate limiting
   - Set requests per window (default: 100)
   - Set window size in seconds (default: 60)

3. **Configure DDoS Detection**
   - Enable DDoS detection
   - Set request threshold (default: 1000)
   - Set detection window (default: 60 seconds)

### Alert Configuration

#### Email Alerts
```
SMTP Server: your-smtp-server.com
SMTP Port: 587
Username: <EMAIL>
Password: your-app-password
From Email: <EMAIL>
To Emails: <EMAIL>,<EMAIL>
Use TLS: Yes
```

#### Webhook Alerts
```
Webhook URL: https://your-webhook-endpoint.com/security
Webhook Secret: your-secret-key (optional)
```

#### Slack Alerts
```
Slack Webhook URL: https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
Slack Channel: #security
```

## Usage

### Security Dashboard

The main dashboard provides:
- **Overview Cards**: Key security metrics at a glance
- **Real-time Charts**: Visual representation of security events
- **Active Alerts**: Current security alerts requiring attention
- **Recent Events**: Latest security events and incidents
- **Top Threats**: Most active threat sources

### Event Management

- **Filter Events**: Filter by type, severity, IP, or time range
- **Event Details**: Detailed view of security event information
- **IP Analysis**: Comprehensive IP address threat analysis
- **Bulk Actions**: Mass operations on security events

### Alert Management

- **Alert Triage**: Acknowledge, resolve, or escalate alerts
- **Bulk Operations**: Handle multiple alerts simultaneously
- **Alert History**: Track alert resolution and response times
- **Custom Actions**: Configure automated responses

## Security Event Types

### Rate Limiting Events
- `rate_limit_exceeded`: IP exceeded configured rate limits
- `endpoint_flooding`: Excessive requests to specific endpoints

### DDoS Detection Events
- `ddos_detected`: Potential DDoS attack identified
- `traffic_spike`: Unusual traffic patterns detected

### Suspicious Activity Events
- `suspicious_activity`: Pattern-based threat detection
- `bot_detection`: Automated bot or scanner activity
- `injection_attempt`: SQL injection or XSS attempts
- `path_traversal`: Directory traversal attempts

### Access Control Events
- `banned_ip_access`: Banned IP attempting access
- `auto_ban`: Automatic IP ban triggered
- `manual_ban`: Manual IP ban applied

## API Endpoints

### Statistics
- `GET /api/v1/plugins/security_monitor/stats` - Get security statistics
- `GET /api/v1/plugins/security_monitor/real-time-stats` - Real-time metrics

### Events
- `GET /api/v1/plugins/security_monitor/events` - List security events
- `POST /api/v1/plugins/security_monitor/events` - Create security event
- `DELETE /api/v1/plugins/security_monitor/events/{id}` - Delete event

### Alerts
- `PATCH /api/v1/plugins/security_monitor/alerts/{id}` - Update alert
- `DELETE /api/v1/plugins/security_monitor/alerts/{id}` - Delete alert
- `POST /api/v1/plugins/security_monitor/alerts/resolve-all` - Resolve all alerts

### IP Management
- `GET /api/v1/plugins/security_monitor/ip-details/{ip}` - Get IP details
- `POST /api/v1/plugins/security_monitor/ban-ip` - Ban IP address

## Performance Considerations

### Resource Usage
- **Memory**: ~50-100MB additional RAM usage
- **CPU**: Minimal impact on request processing
- **Storage**: Event logs scale with activity volume
- **Network**: Minimal overhead for monitoring

### Optimization Tips
1. **Configure appropriate rate limits** based on your traffic patterns
2. **Set reasonable log retention periods** to manage storage
3. **Use efficient alert channels** to minimize notification overhead
4. **Monitor plugin performance** through the dashboard metrics

## Troubleshooting

### Common Issues

1. **Plugin Not Loading**
   - Check plugin is in whitelist
   - Verify file permissions
   - Check CTFd logs for errors

2. **Database Errors**
   - Ensure database has sufficient permissions
   - Check for table creation errors in logs
   - Verify database connectivity

3. **Alert Delivery Issues**
   - Test alert channels using the test function
   - Verify SMTP/webhook configurations
   - Check firewall and network connectivity

4. **High Resource Usage**
   - Adjust rate limiting thresholds
   - Reduce log retention periods
   - Optimize detection patterns

### Debug Mode

Enable debug logging by setting environment variable:
```bash
export SECURITY_MONITOR_DEBUG=1
```

## Security Considerations

1. **Secure Configuration**: Store sensitive credentials securely
2. **Access Control**: Limit admin access to security configuration
3. **Log Protection**: Secure security logs from unauthorized access
4. **Regular Updates**: Keep detection patterns updated
5. **Monitoring**: Monitor the monitor - watch for plugin performance

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This plugin is released under the same license as CTFd.

## Support

For issues and support:
1. Check the troubleshooting section
2. Review CTFd logs for errors
3. Open an issue with detailed information
4. Include configuration and error logs

## Changelog

### Version 1.0.0
- Initial release
- Core security monitoring features
- Real-time dashboard
- Multi-channel alerting
- Comprehensive event logging
- IP banning capabilities
