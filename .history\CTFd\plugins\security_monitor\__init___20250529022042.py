import os
import json
import time
import logging
from datetime import datetime, timedelta
from flask import Blueprint, render_template, request, jsonify, current_app, session, abort
from flask_restx import Namespace, Resource

from CTFd.models import db, Users, Challenges, Submissions, Fails
from CTFd.plugins import (
    register_plugin_assets_directory,
    register_admin_plugin_menu_bar,
    bypass_csrf_protection
)
from CTFd.utils.decorators import authed_only, admins_only
from CTFd.utils import get_config, set_config, user as current_user
from CTFd.utils.user import get_ip
from CTFd.utils.security.csrf import generate_nonce
from CTFd.api import CTFd_API_v1
from CTFd.cache import cache

from .models import SecurityEvent, SecurityConfig, SecurityAlert, SecurityBan, SecurityMetrics, PlatformAnalytics, ContainerMetrics, PerformanceMetrics, create_all
from .utils.detector import SecurityDetector
from .utils.monitor import SecurityMonitor
from .utils.alerting import AlertManager
from .utils.analytics import analytics_collector
from .utils.data_collector import data_collector

def load(app):
    """Load the security monitoring plugin"""
    plugin_name = __name__.split('.')[-1]

    # Initialize database tables
    with app.app_context():
        create_all()

    # Initialize security components
    app.security_detector = SecurityDetector()
    app.security_monitor = SecurityMonitor()
    app.alert_manager = AlertManager()
    app.analytics_collector = analytics_collector
    app.data_collector = data_collector

    # Create local references for use in routes
    security_detector = app.security_detector
    security_monitor = app.security_monitor
    alert_manager = app.alert_manager

    # Start data collection service
    data_collector.start()

    # Register plugin assets
    register_plugin_assets_directory(
        app, base_path=f"/plugins/{plugin_name}/assets",
        endpoint=f'plugins.{plugin_name}.assets'
    )

    # Create blueprint for admin interface
    admin_blueprint = Blueprint(
        "security_monitor_admin",
        __name__,
        template_folder="templates",
        static_folder="assets",
        url_prefix="/plugins/security_monitor/admin"
    )

    # Create API namespace
    security_namespace = Namespace(
        "security_monitor",
        description="Security Monitoring API",
        path="/plugins/security_monitor"
    )

    @admin_blueprint.route('/')
    @admin_blueprint.route('/dashboard')
    @admins_only
    def dashboard():
        """Security monitoring dashboard"""
        # Get recent security events
        recent_events = SecurityEvent.query.order_by(
            SecurityEvent.timestamp.desc()
        ).limit(50).all()

        # Get security statistics
        stats = security_monitor.get_security_stats()

        # Get active alerts
        active_alerts = SecurityAlert.query.filter_by(
            status='active'
        ).order_by(SecurityAlert.created_at.desc()).all()

        return render_template(
            'security_dashboard.html',
            events=recent_events,
            stats=stats,
            alerts=active_alerts,
            nonce=generate_nonce()
        )

    @admin_blueprint.route('/config', methods=['GET', 'POST'])
    @admins_only
    def config():
        """Security configuration panel"""
        if request.method == 'POST':
            data = request.get_json() or request.form

            # Update security configuration
            for key, value in data.items():
                if key.startswith('security_'):
                    SecurityConfig.set_config(key, value)

            return jsonify({'success': True, 'message': 'Configuration updated'})

        # Get current configuration
        config_data = SecurityConfig.get_all_configs()
        return render_template('security_config.html', config=config_data, nonce=generate_nonce())

    @admin_blueprint.route('/events')
    @admins_only
    def events():
        """Security events listing"""
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        event_type = request.args.get('type', '')

        query = SecurityEvent.query
        if event_type:
            query = query.filter_by(event_type=event_type)

        events = query.order_by(
            SecurityEvent.timestamp.desc()
        ).paginate(
            page=page, per_page=per_page, error_out=False
        )

        return render_template('security_events.html', events=events, nonce=generate_nonce())

    @admin_blueprint.route('/alerts')
    @admins_only
    def alerts():
        """Security alerts management"""
        alerts = SecurityAlert.query.order_by(
            SecurityAlert.created_at.desc()
        ).all()

        return render_template('security_alerts.html', alerts=alerts, nonce=generate_nonce())
