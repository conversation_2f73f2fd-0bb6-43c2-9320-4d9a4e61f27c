"""
Enhanced API Security Module for CTF Platforms
Provides specialized protection for CTF challenge endpoints and APIs
"""

import time
import json
import re
import hashlib
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from flask import request, current_app
from CTFd.cache import cache
from CTFd.models import db
from ..models import SecurityEvent, SecurityConfig, SecurityBan

class CTFAPISecurityManager:
    """Specialized security manager for CTF platform APIs"""

    def __init__(self):
        # CTF-specific endpoint patterns and their security requirements
        self.ctf_endpoints = {
            # Challenge-related endpoints
            '/api/v1/challenges': {
                'rate_limit': {'requests': 30, 'window': 60},
                'auth_required': False,
                'sensitive': False,
                'category': 'challenge_listing'
            },
            '/api/v1/challenges/': {  # Individual challenges
                'rate_limit': {'requests': 60, 'window': 60},
                'auth_required': True,
                'sensitive': False,
                'category': 'challenge_access'
            },
            '/api/v1/challenges/.*/attempt': {  # Flag submissions
                'rate_limit': {'requests': 5, 'window': 30},  # Very restrictive
                'auth_required': True,
                'sensitive': True,
                'category': 'flag_submission',
                'special_protection': True
            },
            '/api/v1/challenges/.*/hints': {
                'rate_limit': {'requests': 20, 'window': 60},
                'auth_required': True,
                'sensitive': False,
                'category': 'hint_access'
            },
            
            # User/Team management
            '/api/v1/users': {
                'rate_limit': {'requests': 10, 'window': 60},
                'auth_required': True,
                'sensitive': True,
                'category': 'user_management'
            },
            '/api/v1/teams': {
                'rate_limit': {'requests': 20, 'window': 60},
                'auth_required': True,
                'sensitive': False,
                'category': 'team_access'
            },
            
            # Scoreboard and statistics
            '/api/v1/scoreboard': {
                'rate_limit': {'requests': 30, 'window': 60},
                'auth_required': False,
                'sensitive': False,
                'category': 'scoreboard'
            },
            '/api/v1/statistics': {
                'rate_limit': {'requests': 20, 'window': 60},
                'auth_required': False,
                'sensitive': False,
                'category': 'statistics'
            },
            
            # Admin endpoints
            '/api/v1/admin': {
                'rate_limit': {'requests': 10, 'window': 60},
                'auth_required': True,
                'sensitive': True,
                'category': 'admin',
                'admin_only': True
            },
            
            # File uploads/downloads
            '/api/v1/files': {
                'rate_limit': {'requests': 10, 'window': 300},  # 5 minute window
                'auth_required': True,
                'sensitive': True,
                'category': 'file_access',
                'size_limit': 50 * 1024 * 1024  # 50MB
            }
        }
        
        # Flag submission patterns to detect
        self.flag_patterns = [
            r'(?i)flag\{[^}]+\}',           # flag{...}
            r'(?i)ctf\{[^}]+\}',            # ctf{...}
            r'(?i)[a-f0-9]{32}',            # MD5 hash
            r'(?i)[a-f0-9]{40}',            # SHA1 hash
            r'(?i)[a-f0-9]{64}',            # SHA256 hash
            r'(?i)[a-zA-Z0-9+/]{20,}={0,2}', # Base64
            r'(?i)picoctf\{[^}]+\}',        # picoCTF format
            r'(?i)hackthebox\{[^}]+\}',     # HTB format
        ]
        
        # Suspicious flag submission patterns
        self.suspicious_flag_patterns = [
            r'(?i)(test|admin|password|123|abc)',
            r'(?i)(select|union|insert|delete)',  # SQL injection attempts
            r'(?i)(<script|javascript:)',         # XSS attempts
            r'^.{1,3}$',                          # Very short submissions
            r'^.{200,}$',                         # Very long submissions
        ]
        
        # CTF-specific attack patterns
        self.ctf_attack_patterns = {
            'flag_enumeration': {
                'description': 'Systematic flag enumeration attempt',
                'indicators': [
                    'multiple_challenges_rapid',
                    'sequential_submission_patterns',
                    'automated_timing'
                ]
            },
            'challenge_scraping': {
                'description': 'Challenge content scraping',
                'indicators': [
                    'rapid_challenge_access',
                    'no_submission_attempts',
                    'automated_user_agent'
                ]
            },
            'scoreboard_manipulation': {
                'description': 'Attempt to manipulate scoreboard',
                'indicators': [
                    'rapid_team_switching',
                    'scoreboard_spam_access',
                    'suspicious_point_patterns'
                ]
            },
            'infrastructure_probing': {
                'description': 'CTF infrastructure reconnaissance',
                'indicators': [
                    'admin_endpoint_probing',
                    'file_system_enumeration',
                    'api_fuzzing'
                ]
            }
        }

    def check_api_security(self, endpoint, method, request_obj, user_id=None):
        """
        Comprehensive API security check for CTF endpoints
        Returns dict with security assessment and recommended actions
        """
        security_result = {
            'allowed': True,
            'violations': [],
            'threat_level': 0,
            'recommended_actions': []
        }
        
        try:
            # Match endpoint to CTF configuration
            endpoint_config = self._match_endpoint_config(endpoint)
            
            if endpoint_config:
                # Check rate limiting (enhanced for CTF)
                rate_limit_result = self._check_ctf_rate_limiting(
                    endpoint, method, request_obj, endpoint_config
                )
                if rate_limit_result['violated']:
                    security_result['violations'].append(rate_limit_result)
                    security_result['threat_level'] += rate_limit_result['threat_score']
                
                # Check authentication requirements
                auth_result = self._check_api_authentication(
                    endpoint, user_id, endpoint_config
                )
                if auth_result['violated']:
                    security_result['violations'].append(auth_result)
                    security_result['threat_level'] += auth_result['threat_score']
                
                # Check request validation (CTF-specific)
                validation_result = self._validate_ctf_request(
                    endpoint, method, request_obj, endpoint_config
                )
                if validation_result['violated']:
                    security_result['violations'].append(validation_result)
                    security_result['threat_level'] += validation_result['threat_score']
                
                # Check for CTF-specific attack patterns
                attack_result = self._detect_ctf_attacks(
                    endpoint, method, request_obj, user_id
                )
                if attack_result['detected']:
                    security_result['violations'].append(attack_result)
                    security_result['threat_level'] += attack_result['threat_score']
                
                # Special protection for flag submission endpoints
                if endpoint_config.get('special_protection'):
                    flag_result = self._protect_flag_submission(
                        endpoint, request_obj, user_id
                    )
                    if flag_result['violated']:
                        security_result['violations'].append(flag_result)
                        security_result['threat_level'] += flag_result['threat_score']
            
            # Determine if request should be blocked
            if security_result['threat_level'] >= 8:
                security_result['allowed'] = False
                security_result['recommended_actions'].append('block_request')
            elif security_result['threat_level'] >= 5:
                security_result['recommended_actions'].append('throttle_request')
            elif security_result['threat_level'] >= 3:
                security_result['recommended_actions'].append('monitor_closely')
            
            # Log security assessment
            if security_result['violations']:
                self._log_api_security_event(endpoint, method, security_result, user_id)
            
            return security_result
            
        except Exception as e:
            if current_app:
                current_app.logger.error(f"API security check failed: {str(e)}")
            return {'allowed': True, 'violations': [], 'threat_level': 0, 'recommended_actions': []}

    def _match_endpoint_config(self, endpoint):
        """Match endpoint to configuration using regex patterns"""
        for pattern, config in self.ctf_endpoints.items():
            if re.match(pattern.replace('.*', '[^/]*'), endpoint):
                return config
        return None

    def _check_ctf_rate_limiting(self, endpoint, method, request_obj, config):
        """Enhanced rate limiting for CTF endpoints"""
        ip = self._get_client_ip(request_obj)
        user_agent = request_obj.headers.get('User-Agent', '')
        
        # Get rate limit configuration
        rate_config = config.get('rate_limit', {'requests': 60, 'window': 60})
        base_limit = rate_config['requests']
        window = rate_config['window']
        
        # Adaptive rate limiting based on user behavior
        user_multiplier = self._get_user_rate_multiplier(ip, user_agent)
        adjusted_limit = max(int(base_limit * user_multiplier), 1)
        
        # Create composite key for more granular rate limiting
        rate_key = f"ctf_api_rate:{ip}:{endpoint}:{method}"
        current_count = cache.get(rate_key) or 0
        
        if current_count >= adjusted_limit:
            # Enhanced logging for CTF-specific violations
            violation_details = {
                'endpoint': endpoint,
                'method': method,
                'current_count': current_count,
                'limit': adjusted_limit,
                'window': window,
                'user_multiplier': user_multiplier,
                'category': config.get('category', 'unknown')
            }
            
            return {
                'violated': True,
                'type': 'ctf_api_rate_limit',
                'threat_score': 4 if config.get('sensitive') else 2,
                'details': violation_details
            }
        
        # Update counter
        cache.set(rate_key, current_count + 1, timeout=window)
        
        return {'violated': False, 'threat_score': 0}

    def _check_api_authentication(self, endpoint, user_id, config):
        """Check API authentication requirements"""
        if config.get('auth_required', False):
            if not user_id:
                return {
                    'violated': True,
                    'type': 'missing_authentication',
                    'threat_score': 3,
                    'details': {
                        'endpoint': endpoint,
                        'auth_required': True,
                        'category': config.get('category', 'unknown')
                    }
                }
        
        if config.get('admin_only', False):
            # In production, check if user is admin
            # For now, assume non-admin users
            return {
                'violated': True,
                'type': 'insufficient_privileges',
                'threat_score': 5,
                'details': {
                    'endpoint': endpoint,
                    'required_role': 'admin',
                    'category': config.get('category', 'unknown')
                }
            }
        
        return {'violated': False, 'threat_score': 0}

    def _validate_ctf_request(self, endpoint, method, request_obj, config):
        """CTF-specific request validation"""
        violations = []
        total_threat_score = 0
        
        # Check request size limits
        if config.get('size_limit'):
            content_length = request_obj.headers.get('Content-Length')
            if content_length:
                try:
                    size = int(content_length)
                    if size > config['size_limit']:
                        violations.append({
                            'type': 'request_too_large',
                            'size': size,
                            'limit': config['size_limit']
                        })
                        total_threat_score += 3
                except ValueError:
                    pass
        
        # Validate request headers for API calls
        if method in ['POST', 'PUT', 'PATCH']:
            content_type = request_obj.headers.get('Content-Type', '')
            
            # Check for suspicious content types
            suspicious_types = ['text/html', 'application/x-www-form-urlencoded']
            if any(st in content_type for st in suspicious_types) and 'api' in endpoint:
                violations.append({
                    'type': 'suspicious_content_type',
                    'content_type': content_type
                })
                total_threat_score += 2
        
        # Check for SQL injection in query parameters
        query_string = str(request_obj.query_string)
        if self._contains_sql_injection(query_string):
            violations.append({
                'type': 'sql_injection_attempt',
                'query_string': query_string[:100]  # Truncate for logging
            })
            total_threat_score += 6
        
        # Check for XSS attempts in parameters
        if self._contains_xss_attempt(query_string):
            violations.append({
                'type': 'xss_attempt',
                'query_string': query_string[:100]
            })
            total_threat_score += 5
        
        if violations:
            return {
                'violated': True,
                'type': 'request_validation_failed',
                'threat_score': min(total_threat_score, 10),
                'details': {
                    'endpoint': endpoint,
                    'violations': violations,
                    'category': config.get('category', 'unknown')
                }
            }
        
        return {'violated': False, 'threat_score': 0}

    def _detect_ctf_attacks(self, endpoint, method, request_obj, user_id):
        """Detect CTF-specific attack patterns"""
        ip = self._get_client_ip(request_obj)
        user_agent = request_obj.headers.get('User-Agent', '')
        
        detected_attacks = []
        total_threat_score = 0
        
        # Check for flag enumeration attack
        if self._detect_flag_enumeration(ip, endpoint, user_id):
            detected_attacks.append('flag_enumeration')
            total_threat_score += 6
        
        # Check for challenge scraping
        if self._detect_challenge_scraping(ip, endpoint, user_agent):
            detected_attacks.append('challenge_scraping')
            total_threat_score += 4
        
        # Check for scoreboard manipulation
        if self._detect_scoreboard_manipulation(ip, endpoint, user_id):
            detected_attacks.append('scoreboard_manipulation')
            total_threat_score += 5
        
        # Check for infrastructure probing
        if self._detect_infrastructure_probing(ip, endpoint, method):
            detected_attacks.append('infrastructure_probing')
            total_threat_score += 7
        
        # Check for API fuzzing
        if self._detect_api_fuzzing(ip, endpoint, request_obj):
            detected_attacks.append('api_fuzzing')
            total_threat_score += 5
        
        if detected_attacks:
            return {
                'detected': True,
                'type': 'ctf_attack_pattern',
                'threat_score': min(total_threat_score, 10),
                'details': {
                    'endpoint': endpoint,
                    'attack_types': detected_attacks,
                    'ip': ip,
                    'user_agent': user_agent[:100]
                }
            }
        
        return {'detected': False, 'threat_score': 0}

    def _protect_flag_submission(self, endpoint, request_obj, user_id):
        """Special protection for flag submission endpoints"""
        ip = self._get_client_ip(request_obj)
        violations = []
        total_threat_score = 0
        
        try:
            # Extract flag submission data
            submission_data = self._extract_submission_data(request_obj)
            
            if submission_data:
                # Check submission patterns
                pattern_result = self._analyze_flag_submission_patterns(submission_data, ip, user_id)
                if pattern_result['suspicious']:
                    violations.extend(pattern_result['violations'])
                    total_threat_score += pattern_result['threat_score']
                
                # Check for brute force flag attempts
                brute_force_result = self._detect_flag_brute_force(ip, user_id, submission_data)
                if brute_force_result['detected']:
                    violations.append(brute_force_result)
                    total_threat_score += brute_force_result['threat_score']
                
                # Check submission timing patterns
                timing_result = self._analyze_submission_timing(ip, user_id)
                if timing_result['suspicious']:
                    violations.append(timing_result)
                    total_threat_score += timing_result['threat_score']
                
                # Check for automated submission patterns
                automation_result = self._detect_automated_submissions(ip, submission_data)
                if automation_result['detected']:
                    violations.append(automation_result)
                    total_threat_score += automation_result['threat_score']
        
        except Exception as e:
            if current_app:
                current_app.logger.error(f"Flag submission protection error: {str(e)}")
        
        if violations:
            return {
                'violated': True,
                'type': 'flag_submission_protection',
                'threat_score': min(total_threat_score, 10),
                'details': {
                    'endpoint': endpoint,
                    'violations': violations,
                    'ip': ip,
                    'user_id': user_id
                }
            }
        
        return {'violated': False, 'threat_score': 0}

    def _get_user_rate_multiplier(self, ip, user_agent):
        """Calculate rate limiting multiplier based on user behavior"""
        multiplier = 1.0
        
        # Check user reputation
        cache_key = f"user_reputation:{ip}"
        reputation_data = cache.get(cache_key) or {'score': 0, 'events': []}
        
        # Reduce limits for users with negative reputation
        if reputation_data['score'] < -5:
            multiplier *= 0.3  # Very restrictive
        elif reputation_data['score'] < -2:
            multiplier *= 0.5  # Moderately restrictive
        elif reputation_data['score'] < 0:
            multiplier *= 0.7  # Slightly restrictive
        
        # Check for bot-like user agents
        if any(pattern in user_agent.lower() for pattern in ['bot', 'crawler', 'spider', 'curl', 'wget']):
            multiplier *= 0.2  # Very restrictive for bots
        
        # Check for missing/suspicious user agents
        if not user_agent or len(user_agent) < 10:
            multiplier *= 0.4  # Restrictive for suspicious UAs
        
        return max(multiplier, 0.1)  # Minimum 10% of original limit

    def _detect_flag_enumeration(self, ip, endpoint, user_id):
        """Detect systematic flag enumeration attempts"""
        if 'attempt' not in endpoint:
            return False
        
        cache_key = f"flag_enum:{ip}:{user_id}"
        enum_data = cache.get(cache_key) or {
            'attempts': [],
            'challenges': set(),
            'start_time': time.time()
        }
        
        current_time = time.time()
        enum_data['attempts'].append(current_time)
        
        # Extract challenge ID from endpoint
        challenge_id = self._extract_challenge_id(endpoint)
        if challenge_id:
            enum_data['challenges'].add(challenge_id)
        
        # Keep only recent attempts (last hour)
        cutoff_time = current_time - 3600
        enum_data['attempts'] = [t for t in enum_data['attempts'] if t > cutoff_time]
        
        cache.set(cache_key, enum_data, timeout=3600)
        
        # Detection criteria
        recent_attempts = len(enum_data['attempts'])
        unique_challenges = len(enum_data['challenges'])
        
        # Flag enumeration indicators:
        # 1. Many attempts across multiple challenges
        # 2. Rapid submission rate
        # 3. Low success rate (would need to check actual results)
        
        if recent_attempts > 50 and unique_challenges > 10:  # 50+ attempts on 10+ challenges
            return True
        
        if recent_attempts > 20 and unique_challenges > 5:  # 20+ attempts on 5+ challenges in short time
            time_span = current_time - enum_data['start_time']
            if time_span < 600:  # Within 10 minutes
                return True
        
        return False

    def _detect_challenge_scraping(self, ip, endpoint, user_agent):
        """Detect challenge content scraping"""
        if 'challenges' not in endpoint or 'attempt' in endpoint:
            return False
        
        cache_key = f"challenge_scraping:{ip}"
        scraping_data = cache.get(cache_key) or {
            'accesses': [],
            'user_agents': [],
            'no_submissions': 0
        }
        
        current_time = time.time()
        scraping_data['accesses'].append(current_time)
        scraping_data['user_agents'].append(user_agent)
        
        # Keep only recent data
        cutoff_time = current_time - 1800  # 30 minutes
        scraping_data['accesses'] = [t for t in scraping_data['accesses'] if t > cutoff_time]
        scraping_data['user_agents'] = scraping_data['user_agents'][-50:]  # Last 50 UAs
        
        cache.set(cache_key, scraping_data, timeout=1800)
        
        # Scraping indicators:
        # 1. Many challenge accesses
        # 2. Automated user agent
        # 3. No flag submissions (separate tracking needed)
        
        recent_accesses = len(scraping_data['accesses'])
        
        if recent_accesses > 30:  # 30+ challenge accesses in 30 minutes
            # Check for automated patterns
            unique_uas = len(set(scraping_data['user_agents']))
            if unique_uas == 1:  # Same user agent for all requests
                ua = scraping_data['user_agents'][0] if scraping_data['user_agents'] else ''
                if any(bot_indicator in ua.lower() for bot_indicator in ['curl', 'wget', 'python', 'bot']):
                    return True
        
        return False

    def _detect_scoreboard_manipulation(self, ip, endpoint, user_id):
        """Detect attempts to manipulate scoreboard"""
        if 'scoreboard' not in endpoint and 'teams' not in endpoint:
            return False
        
        cache_key = f"scoreboard_manip:{ip}:{user_id}"
        manip_data = cache.get(cache_key) or {
            'scoreboard_accesses': 0,
            'team_switches': 0,
            'last_access': 0
        }
        
        current_time = time.time()
        
        if 'scoreboard' in endpoint:
            manip_data['scoreboard_accesses'] += 1
        
        if 'teams' in endpoint:
            manip_data['team_switches'] += 1
        
        manip_data['last_access'] = current_time
        
        cache.set(cache_key, manip_data, timeout=3600)
        
        # Manipulation indicators:
        # 1. Excessive scoreboard accesses
        # 2. Rapid team switching
        # 3. Unusual patterns in timing
        
        if manip_data['scoreboard_accesses'] > 100:  # 100+ scoreboard accesses per hour
            return True
        
        if manip_data['team_switches'] > 10:  # 10+ team-related API calls per hour
            return True
        
        return False

    def _detect_infrastructure_probing(self, ip, endpoint, method):
        """Detect CTF infrastructure reconnaissance"""
        cache_key = f"infra_probing:{ip}"
        probing_data = cache.get(cache_key) or {
            'admin_attempts': 0,
            'file_enum_attempts': 0,
            'api_fuzzing_attempts': 0,
            'unusual_methods': 0
        }
        
        # Track different types of probing
        if 'admin' in endpoint:
            probing_data['admin_attempts'] += 1
        
        if 'files' in endpoint or 'download' in endpoint:
            probing_data['file_enum_attempts'] += 1
        
        if method in ['OPTIONS', 'HEAD', 'TRACE']:
            probing_data['unusual_methods'] += 1
        
        # API fuzzing detection
        if self._is_api_fuzzing_pattern(endpoint):
            probing_data['api_fuzzing_attempts'] += 1
        
        cache.set(cache_key, probing_data, timeout=3600)
        
        # Probing indicators
        total_probing_score = (
            probing_data['admin_attempts'] * 3 +
            probing_data['file_enum_attempts'] * 2 +
            probing_data['api_fuzzing_attempts'] * 2 +
            probing_data['unusual_methods'] * 1
        )
        
        return total_probing_score > 10

    def _detect_api_fuzzing(self, ip, endpoint, request_obj):
        """Detect API fuzzing attempts"""
        cache_key = f"api_fuzzing:{ip}"
        fuzzing_data = cache.get(cache_key) or {
            'endpoints': [],
            'error_responses': 0,
            'unusual_params': 0
        }
        
        fuzzing_data['endpoints'].append(endpoint)
        
        # Keep only recent endpoints
        fuzzing_data['endpoints'] = fuzzing_data['endpoints'][-100:]
        
        # Check for fuzzing patterns
        unique_endpoints = len(set(fuzzing_data['endpoints']))
        total_requests = len(fuzzing_data['endpoints'])
        
        # High diversity in endpoints suggests fuzzing
        if total_requests > 20 and unique_endpoints > 15:
            return True
        
        # Check for systematic endpoint enumeration
        if self._is_systematic_enumeration(fuzzing_data['endpoints']):
            return True
        
        cache.set(cache_key, fuzzing_data, timeout=1800)
        
        return False

    def _analyze_flag_submission_patterns(self, submission_data, ip, user_id):
        """Analyze flag submission for suspicious patterns"""
        violations = []
        threat_score = 0
        
        flag_content = submission_data.get('flag', '')
        
        # Check for suspicious patterns in flag
        for pattern in self.suspicious_flag_patterns:
            if re.search(pattern, flag_content):
                violations.append({
                    'type': 'suspicious_flag_pattern',
                    'pattern': pattern[:50],
                    'flag_length': len(flag_content)
                })
                threat_score += 2
        
        # Check flag format validity
        valid_format = False
        for pattern in self.flag_patterns:
            if re.search(pattern, flag_content):
                valid_format = True
                break
        
        if not valid_format and len(flag_content) > 5:
            violations.append({
                'type': 'invalid_flag_format',
                'flag_length': len(flag_content)
            })
            threat_score += 1
        
        # Check for common testing strings
        test_strings = ['test', 'admin', 'password', '123456', 'flag', 'ctf']
        if flag_content.lower() in test_strings:
            violations.append({
                'type': 'test_string_submission',
                'content': flag_content.lower()
            })
            threat_score += 3
        
        return {
            'suspicious': len(violations) > 0,
            'violations': violations,
            'threat_score': threat_score
        }

    def _detect_flag_brute_force(self, ip, user_id, submission_data):
        """Detect brute force flag submission attempts"""
        challenge_id = submission_data.get('challenge_id')
        if not challenge_id:
            return {'detected': False, 'threat_score': 0}
        
        cache_key = f"flag_brute:{ip}:{user_id}:{challenge_id}"
        brute_data = cache.get(cache_key) or {
            'attempts': [],
            'failed_attempts': 0,
            'success_count': 0
        }
        
        current_time = time.time()
        brute_data['attempts'].append(current_time)
        
        # Keep only recent attempts (last 30 minutes)
        cutoff_time = current_time - 1800
        brute_data['attempts'] = [t for t in brute_data['attempts'] if t > cutoff_time]
        
        cache.set(cache_key, brute_data, timeout=1800)
        
        recent_attempts = len(brute_data['attempts'])
        
        # Brute force indicators
        if recent_attempts > 20:  # 20+ attempts on same challenge in 30 minutes
            return {
                'detected': True,
                'type': 'flag_brute_force',
                'threat_score': 6,
                'details': {
                    'challenge_id': challenge_id,
                    'attempt_count': recent_attempts,
                    'time_window': '30_minutes'
                }
            }
        
        return {'detected': False, 'threat_score': 0}

    def _analyze_submission_timing(self, ip, user_id):
        """Analyze submission timing for automation detection"""
        cache_key = f"submission_timing:{ip}:{user_id}"
        timing_data = cache.get(cache_key) or {'timestamps': []}
        
        current_time = time.time()
        timing_data['timestamps'].append(current_time)
        
        # Keep only recent timestamps
        timing_data['timestamps'] = timing_data['timestamps'][-50:]
        
        cache.set(cache_key, timing_data, timeout=3600)
        
        if len(timing_data['timestamps']) >= 10:
            # Calculate intervals between submissions
            intervals = []
            for i in range(1, len(timing_data['timestamps'])):
                interval = timing_data['timestamps'][i] - timing_data['timestamps'][i-1]
                intervals.append(interval)
            
            # Check for overly regular intervals (automation indicator)
            if len(intervals) >= 5:
                avg_interval = sum(intervals) / len(intervals)
                
                # Very regular intervals suggest automation
                regular_count = sum(1 for i in intervals if abs(i - avg_interval) < 1.0)
                regularity = regular_count / len(intervals)
                
                if regularity > 0.8 and avg_interval < 10:  # 80% regular, less than 10 seconds apart
                    return {
                        'suspicious': True,
                        'type': 'automated_timing',
                        'threat_score': 4,
                        'details': {
                            'regularity': regularity,
                            'avg_interval': avg_interval,
                            'sample_size': len(intervals)
                        }
                    }
        
        return {'suspicious': False, 'threat_score': 0}

    def _detect_automated_submissions(self, ip, submission_data):
        """Detect automated flag submission patterns"""
        cache_key = f"auto_submissions:{ip}"
        auto_data = cache.get(cache_key) or {
            'submissions': [],
            'patterns': defaultdict(int)
        }
        
        # Track submission patterns
        flag_content = submission_data.get('flag', '')
        
        # Check for patterns in submission content
        if len(flag_content) > 0:
            # Track character patterns
            char_pattern = ''.join(sorted(set(flag_content.lower())))
            auto_data['patterns'][char_pattern] += 1
            
            # Track length patterns
            length_pattern = f"len_{len(flag_content)}"
            auto_data['patterns'][length_pattern] += 1
        
        auto_data['submissions'].append({
            'timestamp': time.time(),
            'flag_length': len(flag_content),
            'flag_hash': hashlib.md5(flag_content.encode()).hexdigest()[:8]
        })
        
        # Keep only recent submissions
        auto_data['submissions'] = auto_data['submissions'][-100:]
        
        cache.set(cache_key, auto_data, timeout=3600)
        
        # Detection logic
        if len(auto_data['submissions']) >= 20:
            # Check for repeated patterns
            max_pattern_count = max(auto_data['patterns'].values())
            if max_pattern_count > 10:  # Same pattern repeated 10+ times
                return {
                    'detected': True,
                    'type': 'automated_submission_pattern',
                    'threat_score': 5,
                    'details': {
                        'max_pattern_count': max_pattern_count,
                        'total_submissions': len(auto_data['submissions'])
                    }
                }
        
        return {'detected': False, 'threat_score': 0}

    # Helper methods
    
    def _get_client_ip(self, request_obj):
        """Get client IP address from request"""
        # Check for forwarded headers
        forwarded_for = request_obj.headers.get('X-Forwarded-For')
        if forwarded_for:
            return forwarded_for.split(',')[0].strip()
        
        real_ip = request_obj.headers.get('X-Real-IP')
        if real_ip:
            return real_ip
        
        return request_obj.remote_addr

    def _extract_submission_data(self, request_obj):
        """Extract flag submission data from request"""
        try:
            if request_obj.method == 'POST':
                if request_obj.is_json:
                    data = request_obj.get_json()
                    return {
                        'flag': data.get('submission', ''),
                        'challenge_id': data.get('challenge_id'),
                        'method': 'json'
                    }
                else:
                    # Form data
                    return {
                        'flag': request_obj.form.get('submission', ''),
                        'challenge_id': request_obj.form.get('challenge_id'),
                        'method': 'form'
                    }
            return None
        except Exception:
            return None

    def _extract_challenge_id(self, endpoint):
        """Extract challenge ID from endpoint"""
        match = re.search(r'/challenges/(\d+)', endpoint)
        return match.group(1) if match else None

    def _contains_sql_injection(self, query_string):
        """Check for SQL injection patterns"""
        sql_patterns = [
            r'(?i)(union|select|insert|delete|drop|create|alter)\s+',
            r'(?i)(or|and)\s+[\d\w]+\s*=\s*[\d\w]+',
            r'(?i)(\'|\").*(\bor\b|\band\b).*(\1)',
            r'(?i)(sleep|waitfor|delay|benchmark)\s*\(',
        ]
        
        for pattern in sql_patterns:
            if re.search(pattern, query_string):
                return True
        return False

    def _contains_xss_attempt(self, query_string):
        """Check for XSS patterns"""
        xss_patterns = [
            r'(?i)<script[^>]*>',
            r'(?i)javascript:',
            r'(?i)on(load|error|click|mouse)\s*=',
            r'(?i)(eval|alert|confirm|prompt)\s*\(',
        ]
        
        for pattern in xss_patterns:
            if re.search(pattern, query_string):
                return True
        return False

    def _is_api_fuzzing_pattern(self, endpoint):
        """Check if endpoint matches API fuzzing patterns"""
        fuzzing_indicators = [
            r'/api/v\d+/\w+/\d+/\w+',  # Deep API paths
            r'/api/.*\.(json|xml|txt|php|asp)',  # File extensions in API
            r'/api/.*[<>{}()[\]";\'|&]',  # Special characters
        ]
        
        for pattern in fuzzing_indicators:
            if re.search(pattern, endpoint):
                return True
        return False

    def _is_systematic_enumeration(self, endpoints):
        """Check if endpoints show systematic enumeration pattern"""
        if len(endpoints) < 10:
            return False
        
        # Look for sequential patterns
        api_endpoints = [ep for ep in endpoints if '/api/' in ep]
        if len(api_endpoints) < len(endpoints) * 0.8:  # 80% API calls
            return False
        
        # Check for ID enumeration patterns
        id_patterns = []
        for endpoint in api_endpoints:
            match = re.search(r'/(\d+)(?:/|$)', endpoint)
            if match:
                id_patterns.append(int(match.group(1)))
        
        if len(id_patterns) >= 5:
            # Check if IDs are sequential or follow a pattern
            sorted_ids = sorted(id_patterns)
            sequential_count = 0
            for i in range(1, len(sorted_ids)):
                if sorted_ids[i] - sorted_ids[i-1] <= 2:  # Close sequential
                    sequential_count += 1
            
            if sequential_count / len(sorted_ids) > 0.7:  # 70% sequential
                return True
        
        return False

    def _log_api_security_event(self, endpoint, method, security_result, user_id):
        """Log API security events"""
        try:
            ip = request.remote_addr if hasattr(request, 'remote_addr') else 'unknown'
            
            # Determine event type and severity
            if security_result['threat_level'] >= 8:
                event_type = 'api_security_critical'
                severity = 'critical'
            elif security_result['threat_level'] >= 5:
                event_type = 'api_security_high'
                severity = 'high'
            else:
                event_type = 'api_security_medium'
                severity = 'medium'
            
            # Create security event
            event = SecurityEvent(
                event_type=event_type,
                severity=severity,
                source_ip=ip,
                user_id=user_id,
                endpoint=endpoint,
                method=method,
                details={
                    'threat_level': security_result['threat_level'],
                    'violations': security_result['violations'],
                    'recommended_actions': security_result['recommended_actions'],
                    'api_security': True
                },
                timestamp=datetime.utcnow()
            )
            
            db.session.add(event)
            db.session.commit()
            
        except Exception as e:
            if current_app:
                current_app.logger.error(f"Failed to log API security event: {str(e)}")

    def get_api_security_stats(self):
        """Get API security statistics"""
        try:
            last_24h = datetime.utcnow() - timedelta(hours=24)
            
            stats = {
                'total_api_events': SecurityEvent.query.filter(
                    SecurityEvent.timestamp >= last_24h,
                    SecurityEvent.details.contains('"api_security": true')
                ).count(),
                
                'blocked_requests': SecurityEvent.query.filter(
                    SecurityEvent.timestamp >= last_24h,
                    SecurityEvent.event_type.in_(['api_security_critical', 'api_security_high'])
                ).count(),
                
                'flag_submission_events': SecurityEvent.query.filter(
                    SecurityEvent.timestamp >= last_24h,
                    SecurityEvent.event_type.like('%flag_submission%')
                ).count(),
                
                'top_violated_endpoints': self._get_top_violated_endpoints(),
                'api_attack_trends': self._get_api_attack_trends()
            }
            
            return stats
            
        except Exception as e:
            if current_app:
                current_app.logger.error(f"Failed to get API security stats: {str(e)}")
            return {}

    def _get_top_violated_endpoints(self):
        """Get endpoints with most security violations"""
        try:
            last_24h = datetime.utcnow() - timedelta(hours=24)
            
            # This would require more complex SQL in production
            events = SecurityEvent.query.filter(
                SecurityEvent.timestamp >= last_24h,
                SecurityEvent.details.contains('"api_security": true')
            ).all()
            
            endpoint_counts = Counter()
            for event in events:
                if event.endpoint:
                    endpoint_counts[event.endpoint] += 1
            
            return [{'endpoint': ep, 'violations': count} 
                   for ep, count in endpoint_counts.most_common(10)]
            
        except Exception:
            return []

    def _get_api_attack_trends(self):
        """Get API attack trends"""
        try:
            trends = []
            for i in range(7):
                day_start = datetime.utcnow() - timedelta(days=i+1)
                day_end = day_start + timedelta(days=1)
                
                day_events = SecurityEvent.query.filter(
                    SecurityEvent.timestamp >= day_start,
                    SecurityEvent.timestamp < day_end,
                    SecurityEvent.details.contains('"api_security": true')
                ).count()
                
                trends.append({
                    'date': day_start.strftime('%Y-%m-%d'),
                    'events': day_events
                })
            
            return list(reversed(trends))
            
        except Exception:
            return []
