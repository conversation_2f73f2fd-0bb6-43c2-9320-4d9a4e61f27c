@echo off
echo ========================================
echo   RESTORING TO CHECKPOINT 4 STATE
echo   Complete CTFd + Monitoring Restoration
echo ========================================

echo [1/10] Stopping all services...
docker-compose down -v
timeout /t 5 >nul

echo [2/10] Cleaning up broken configurations...
REM Remove broken plugin configurations
if exist "CTFd\plugins\security_monitor" (
    echo Disabling broken security_monitor plugin...
    ren "CTFd\plugins\security_monitor" "CTFd\plugins\security_monitor.disabled"
)

if exist "CTFd\plugins\enhanced_security" (
    echo Disabling enhanced_security plugin...
    ren "CTFd\plugins\enhanced_security" "CTFd\plugins\enhanced_security.disabled"
)

if exist "CTFd\plugins\simple_security" (
    echo Disabling simple_security plugin...
    ren "CTFd\plugins\simple_security" "CTFd\plugins\simple_security.disabled"
)

echo [3/10] Restoring core plugin configuration...
REM Restore working plugins
if exist "CTFd\plugins\security_monitor.disabled" (
    echo Re-enabling security_monitor plugin...
    ren "CTFd\plugins\security_monitor.disabled" "CTFd\plugins\security_monitor"
)

echo [4/10] Cleaning database volumes...
docker volume rm ctfd_with_ctfd-whale_grafana-clean 2>nul
docker volume rm ctfd_with_ctfd-whale_loki-data 2>nul
docker volume rm ctfd_with_ctfd-whale_prometheus-data 2>nul

echo [5/10] Restoring working docker-compose configuration...
REM The docker-compose.yml should be restored to working state

echo [6/10] Restoring working Grafana configuration...
REM Clean up broken dashboard
if exist "conf\grafana\dashboards\security-monitor-dashboard.json.backup" (
    copy "conf\grafana\dashboards\security-monitor-dashboard.json.backup" "conf\grafana\dashboards\security-monitor-dashboard.json"
)

echo [7/10] Building fresh CTFd image...
docker-compose build ctfd

echo [8/10] Starting core services (database, cache, CTFd)...
docker-compose up -d db cache
timeout /t 15 >nul
docker-compose up -d ctfd
timeout /t 20 >nul

echo [9/10] Starting monitoring stack...
docker-compose up -d loki prometheus
timeout /t 15 >nul
docker-compose up -d grafana promtail
timeout /t 10 >nul

echo [10/10] Starting remaining services...
docker-compose up -d nginx frps frpc
timeout /t 10 >nul

echo ========================================
echo   RESTORATION COMPLETE!
echo ========================================
echo.
echo Services should now be available at:
echo   - CTFd: http://localhost:82
echo   - Grafana: http://localhost:3000 (admin/admin)
echo   - Prometheus: http://localhost:9090
echo.
echo Checking service status...
docker-compose ps

echo.
echo If any services are not running, try:
echo   docker-compose logs [service-name]
echo.
echo Restoration to Checkpoint 4 state complete!
pause
